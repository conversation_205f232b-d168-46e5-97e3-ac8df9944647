#!/usr/bin/env python3
import sys
import time
import threading
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QLineEdit, QTextEdit, 
                             QProgressBar, QGroupBox, QGridLayout, QSpinBox,
                             QMessageBox, QTabWidget, QTableWidget, QTableWidgetItem,
                             QHeaderView, QCheckBox, QComboBox)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont
import requests
from urllib.parse import quote
import random
import string
import json

class SingleAccountWorker:
    """Worker class để tạo một tài khoản duy nhất"""
    
    def __init__(self, password, worker_id=0):
        self.password = password
        self.worker_id = worker_id
        self.running = True
        # Tạo session riêng cho mỗi worker
        self.session = requests.Session()
        self.setup_session_headers()
    
    def setup_session_headers(self):
        """Setup session với headers chuẩn"""
        self.session.headers.update({
            'accept': '*/*',
            'accept-language': 'vi,en-US;q=0.9,en;q=0.8',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        })
        
        # Tạo initial cookies để giống browser thật
        self.session.cookies.set('weblogger_did', f'web_{random.randint(100000000000, 999999999999):X}', domain='.klingai.com')
        self.session.cookies.set('__risk_web_device_id', f'{random.randint(100000000000000, 999999999999999)}', domain='.klingai.com')
        self.session.cookies.set('KLING_LAST_ACCESS_REGION', 'global', domain='.klingai.com')
        self.session.cookies.set('_did', f'web_{random.randint(10000000000000, 99999999999999):X}', domain='.klingai.com')
        self.session.cookies.set('did', f'web_{random.randint(100000000000000, 999999999999999)}', domain='.klingai.com')
        
        # Thêm các cookies analytics cơ bản
        import time
        current_time = int(time.time())
        self.session.cookies.set('_gcl_au', f'1.1.{random.randint(100000000, 999999999)}.{current_time}', domain='.klingai.com')
        self.session.cookies.set('_ga', f'GA1.1.{random.randint(1000000000, 9999999999)}.{current_time}', domain='.klingai.com')
        self.session.cookies.set('_clck', f'{random.randint(1000000, 9999999)}%7C2%7Cfwy%7C0%7C{random.randint(1000, 9999)}', domain='.klingai.com')
    
    def generate_random_email(self):
        """Tạo random email name (10-14 ký tự) với domain simpace.edu.vn"""
        username_length = random.randint(10, 14)
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=username_length))
        email = f"{username}@simpace.edu.vn"
        return email, username

    def get_verification_code_from_tempmail(self, mail_name, max_retries=10, wait_time=5):
        """Lấy mã xác thực từ tempmail sử dụng API hunght1890.com"""
        url = f"https://hunght1890.com/{mail_name}%40simpace.edu.vn"
        
        headers = {
            'accept': '*/*',
            'accept-language': 'vi',
            'priority': 'u=1, i',
            'referer': 'https://hunght1890.com/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
        }
        
        for attempt in range(max_retries):
            if not self.running:
                return None
                
            try:
                response = requests.get(url, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    try:
                        emails = response.json()
                        
                        for email in emails:
                            subject = email.get('subject', '')
                            body = email.get('body', '')
                            
                            # Kiểm tra email từ KlingAI
                            if 'klingai' in subject.lower() or 'kling' in subject.lower():
                                # Extract mã xác thực 6 số
                                import re
                                code_match = re.search(r'\b\d{6}\b', body)
                                if code_match:
                                    code = code_match.group()
                                    return code
                        
                        if len(emails) == 0:
                            time.sleep(wait_time)
                            
                    except json.JSONDecodeError:
                        pass
                        
            except Exception:
                pass
                
            if attempt < max_retries - 1:
                time.sleep(wait_time)
        
        return None
    
    def get_code(self, email):
        """Gửi yêu cầu mã xác thực sử dụng session"""
        url = "https://id.klingai.com/pass/ksi18n/web/email/code?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR85WymFzHoNZcUWNcTiC8KvwzS6UWycXZhGA6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UjA8hnlVOU.$HE_f2ed751012bfb45f292eb827258364dd73b8b9b9b9b870211503a967cd0a55321ae12eb822ef8763c2ef8751b9&caver=2"
        
        encoded_email = quote(email, safe='')
        payload = f'sid=ksi18n.ai.portal&email={encoded_email}&type=439&language=en&isWebSig4=true'
        
        headers = {
            'content-type': 'application/x-www-form-urlencoded',
            'fe-id': '4d0021ac1750482685583251',
            'fe-kpf': 'PC',
            'fe-kpn': 'KLING',
            'origin': 'https://app.klingai.com',
            'referer': 'https://app.klingai.com/',
        }
        
        try:
            response = self.session.post(url, headers=headers, data=payload, timeout=10)
            return response.status_code == 200
        except Exception:
            return False
    
    def register(self, email, password, code):
        """Đăng ký tài khoản sử dụng session"""
        url = "https://id.klingai.com/pass/ksi18n/web/register/emailPassword?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR845y2FzHoNfcUWNcTiC8KvwzS6UWycXZhGB6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UnA8hnlVOU.$HE_c7d84025278a816a1c1b8df76e0a21ae988d8c8c8c8d441420369905aa3e21dc2fd41b8d17dab256f7dab2648c&caver=2"

        encoded_email = quote(email, safe='')
        encoded_password = quote(password, safe='')
        payload = f'sid=ksi18n.ai.portal&email={encoded_email}&emailCode={code}&password={encoded_password}&setCookie=true&language=en&isWebSig4=true'

        headers = {
            'content-type': 'application/x-www-form-urlencoded',
            'fe-id': '4d0021ac1750482685583251',
            'fe-kpf': 'PC',
            'fe-kpn': 'KLING',
            'origin': 'https://app.klingai.com',
            'referer': 'https://app.klingai.com/',
        }

        try:
            response = self.session.post(url, headers=headers, data=payload, timeout=10)
            
            # Parse response để lấy thêm cookies từ JSON
            if response.status_code == 200:
                try:
                    response_data = json.loads(response.text)
                    
                    # Extract cookies từ response JSON
                    if 'ksi18n.ai.portal_st' in response_data:
                        self.session.cookies.set('ksi18n.ai.portal_st', response_data['ksi18n.ai.portal_st'], domain='.klingai.com')
                    
                    if 'ksi18n.ai.portal_at' in response_data:
                        self.session.cookies.set('ksi18n.ai.portal_at', response_data['ksi18n.ai.portal_at'], domain='.klingai.com')
                    
                    if 'passToken' in response_data:
                        self.session.cookies.set('passToken', response_data['passToken'], domain='.klingai.com')
                    
                    if 'ksi18n.ai.portal_ph' in response_data:
                        self.session.cookies.set('ksi18n.ai.portal_ph', response_data['ksi18n.ai.portal_ph'], domain='.klingai.com')
                    
                    if 'userId' in response_data:
                        self.session.cookies.set('userId', str(response_data['userId']), domain='.klingai.com')
                        
                except json.JSONDecodeError:
                    pass
            
            return response.status_code == 200, response.text
        except Exception as e:
            return False, str(e)
    
    def extract_cookies_from_session(self):
        """Extract cookies từ session theo format chuẩn"""
        cookie_list = []
        
        # Thứ tự ưu tiên cookies
        priority_order = [
            'weblogger_did', '__risk_web_device_id', 'KLING_LAST_ACCESS_REGION', 
            '_did', 'did', '_gcl_gs', '_gcl_au', '_gcl_aw', '_ga', '_clck', 
            'userId', 'ksi18n.ai.portal_st', 'ksi18n.ai.portal_at', 'ksi18n.ai.portal_ph',
            'passToken', 'ak_bmsc', '_clsk', '_ga_MWG30LDQKZ', '_uetsid', '_uetvid', 'bm_sv'
        ]
        
        # Dictionary để lưu cookies
        cookies_dict = {}
        
        # Lấy tất cả cookies từ session
        for cookie in self.session.cookies:
            cookies_dict[cookie.name] = cookie.value
        
        # Thêm cookies theo thứ tự ưu tiên
        for cookie_name in priority_order:
            if cookie_name in cookies_dict:
                cookie_list.append(f"{cookie_name}={cookies_dict[cookie_name]}")
        
        # Thêm các cookies còn lại
        for cookie_name, cookie_value in cookies_dict.items():
            if cookie_name not in priority_order:
                cookie_list.append(f"{cookie_name}={cookie_value}")
        
        return '; '.join(cookie_list)
    
    def extract_user_id_from_response(self, response_text):
        """Extract userId từ response JSON"""
        try:
            response_data = json.loads(response_text)
            user_id = response_data.get('userId')
            if user_id:
                return str(user_id)
        except:
            pass
        return None
    
    def create_single_account(self):
        """Tạo một tài khoản duy nhất"""
        try:
            # Bước 1: Tạo random email
            email, mail_name = self.generate_random_email()
            
            # Bước 2: Gửi yêu cầu mã xác thực
            if not self.get_code(email):
                return False, f"{email}:FAILED_SEND_CODE"
            
            # Bước 3: Đợi và lấy mã xác thực
            time.sleep(10)  # Đợi email được gửi
            verification_code = self.get_verification_code_from_tempmail(mail_name)
            
            if not verification_code:
                return False, f"{email}:FAILED_GET_CODE"
            
            # Bước 4: Đăng ký tài khoản
            success, response = self.register(email, self.password, verification_code)
            
            if success:
                # Extract cookies và user ID
                cookie_string = self.extract_cookies_from_session()
                user_id = self.extract_user_id_from_response(response)
                
                account_info = {
                    'user_id': user_id or 'UNKNOWN',
                    'email': email,
                    'password': self.password,
                    'cookie_string': cookie_string
                }
                return True, json.dumps(account_info)
            else:
                return False, f"{email}:FAILED_REGISTER"
                
        except Exception as e:
            return False, f"ERROR:{str(e)}"

class MultiThreadWorker(QThread):
    """Worker thread để quản lý đa luồng tạo tài khoản"""
    log_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)
    email_created_signal = pyqtSignal(str, str)  # email, mail_name
    registration_complete_signal = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, password, count=1, max_threads=3):
        super().__init__()
        self.password = password
        self.count = count
        self.max_threads = max_threads
        self.running = True
        self.completed_count = 0
        self.lock = threading.Lock()
    
    def stop(self):
        self.running = False
    
    def worker_task(self, worker_id):
        """Task cho mỗi worker thread"""
        worker = SingleAccountWorker(self.password, worker_id)
        worker.running = self.running
        
        success, result = worker.create_single_account()
        
        with self.lock:
            self.completed_count += 1
            progress = int((self.completed_count / self.count) * 100)
            self.progress_signal.emit(progress)
            
            if success:
                # Parse account info để emit signals
                try:
                    account_data = json.loads(result)
                    email = account_data['email']
                    mail_name = email.split('@')[0]
                    self.email_created_signal.emit(email, mail_name)
                    self.log_signal.emit(f"✅ Worker {worker_id}: Tạo thành công {email}")
                except:
                    pass
            else:
                self.log_signal.emit(f"❌ Worker {worker_id}: {result}")
            
            self.registration_complete_signal.emit(success, result)
        
        return success, result
    
    def run(self):
        """Chạy đa luồng tạo tài khoản"""
        self.log_signal.emit(f"🚀 Bắt đầu tạo {self.count} tài khoản với {self.max_threads} luồng song song")
        
        # Sử dụng ThreadPoolExecutor để quản lý đa luồng
        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            # Submit tất cả tasks
            futures = []
            for i in range(self.count):
                if not self.running:
                    break
                future = executor.submit(self.worker_task, i + 1)
                futures.append(future)
                
                # Delay nhỏ giữa các task để tránh spam
                time.sleep(1)
            
            # Đợi tất cả tasks hoàn thành
            for future in as_completed(futures):
                if not self.running:
                    break
                try:
                    future.result()  # Chỉ cần đợi hoàn thành, kết quả đã được xử lý trong worker_task
                except Exception as e:
                    self.log_signal.emit(f"❌ Task error: {str(e)}")
        
        self.log_signal.emit(f"🎉 Hoàn thành! Đã tạo {self.completed_count}/{self.count} tài khoản")

if __name__ == "__main__":
    print("🧪 Testing MultiThread Worker...")
    # Test code here
