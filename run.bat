@echo off
echo ========================================
echo    Kling AI Auto Register Tool
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    pause
    exit /b 1
)

echo [INFO] Python found
echo.

REM Check if requirements are installed
echo [INFO] Checking dependencies...
pip show PyQt5 >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo [INFO] Dependencies already installed
)

echo.
echo [INFO] Starting Kling AI Auto Register Tool...
echo.

REM Run the application
python kling_register_gui.py

if errorlevel 1 (
    echo.
    echo [ERROR] Application crashed or exited with error
    pause
)

echo.
echo [INFO] Application closed
pause
