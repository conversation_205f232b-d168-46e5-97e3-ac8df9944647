# 🤖 Kling AI Auto Register Tool

Tool tự động đăng ký tài khoản Kling AI với giao diện đồ họa PyQt5.

## ✨ Tính năng

- 🚀 **Tự động tạo email temp** sử dụng API tempmail.id.vn
- 📧 **Tự động nhận mã xác thực** từ email
- 🔐 **Tự động đăng ký tài khoản** Kling AI
- 📊 **Giao diện đồ họa** thân thiện với PyQt5
- 📈 **Thống kê real-time** số tài khoản thành công/thất bại
- 💾 **Xuất kết quả** ra file text
- ⏹️ **Dừng/tiếp tục** quá trình bất cứ lúc nào

## 🛠️ Cài đặt

### 1. Clone repository
```bash
git clone <repository-url>
cd MIT-Kling-Register
```

### 2. Cài đặt Python dependencies
```bash
pip install -r requirements.txt
```

### 3. Chạy ứng dụng
```bash
python kling_register_gui.py
```

## 📋 Hướng dẫn sử dụng

### 1. **Tab Đăng ký**
- **Mật khẩu**: Nhập mật khẩu muốn sử dụng cho tất cả tài khoản (mặc định: `Anhtu3112@`)
- **Số lượng tài khoản**: Chọn số tài khoản muốn tạo (1-100)
- **Hiện mật khẩu**: Checkbox để hiển thị/ẩn mật khẩu
- **Bắt đầu đăng ký**: Bắt đầu quá trình tự động
- **Dừng**: Dừng quá trình đang chạy

### 2. **Tab Kết quả**
- **Thống kê**: Hiển thị số tài khoản thành công/thất bại
- **Bảng kết quả**: Danh sách chi tiết tất cả tài khoản
- **Xuất file**: Xuất tài khoản thành công hoặc tất cả ra file

## 🔧 Cấu hình

### API Token tempmail.id.vn
Token hiện tại: `4890|AEunrjmEr5f1B5i6UwNOlpWfKaTz8bBcJf34vu2m73c5ca98`

### Domain email
Domain sử dụng: `tempmail.ckvn.edu.vn`

### Thay đổi cấu hình
Để thay đổi token hoặc domain, chỉnh sửa trong class `EmailWorker`:
```python
self.token = "YOUR_NEW_TOKEN"
self.domain = "YOUR_DOMAIN"
```

## 📁 File output

### successful_accounts.txt
```
# Tài khoản Kling AI đăng ký thành công
# Format: email:password

<EMAIL>:Anhtu3112@
<EMAIL>:Anhtu3112@
```

### all_accounts.txt
```
# Tất cả tài khoản Kling AI
# Format: email:password:status

# Tài khoản thành công:
<EMAIL>:Anhtu3112@:SUCCESS

# Tài khoản thất bại:
<EMAIL>:Anhtu3112@:FAILED
```

## 🚨 Lưu ý

- Tool sử dụng API tempmail.id.vn miễn phí, có giới hạn rate limit
- Mỗi lần đăng ký sẽ đợi 5 giây để tránh spam
- Mã xác thực có thời gian chờ tối đa 30 giây
- Nên sử dụng với số lượng vừa phải để tránh bị block

## 🐛 Troubleshooting

### Lỗi "Không thể tạo email temp"
- Kiểm tra kết nối internet
- Kiểm tra token API còn hạn không
- Thử lại sau vài phút

### Lỗi "Không nhận được mã xác thực"
- Email có thể bị delay
- Kiểm tra spam folder (trong trường hợp này là API)
- Thử với email khác

### Lỗi "Đăng ký thất bại"
- Kling AI có thể đã block IP
- Thử đổi VPN/proxy
- Kiểm tra mật khẩu có đúng format không

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng tạo issue trên GitHub hoặc liên hệ qua:
- Email: <EMAIL>
- Telegram: @your_telegram

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.
