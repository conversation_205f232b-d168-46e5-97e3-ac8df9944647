import requests
from urllib.parse import quote
import random
import string
import json
import re
import time

def generate_random_email():
    """Tạo random email name (10-14 ký tự) với domain simpace.edu.vn"""
    username_length = random.randint(10, 14)
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=username_length))
    email = f"{username}@simpace.edu.vn"
    return email, username

def get_verification_code_from_tempmail(mail_name, max_retries=10, wait_time=5):
    """
    Lấy mã xác thực từ tempmail sử dụng API hunght1890.com
    Args:
        mail_name: Tên mail (không bao gồm @simpace.edu.vn)
        max_retries: Số lần thử tối đa
        wait_time: Thời gian chờ giữa các lần thử (giây)
    Returns:
        verification_code hoặc None nếu không tìm thấy
    """
    url = f"https://hunght1890.com/{mail_name}%40simpace.edu.vn"

    headers = {
        'accept': '*/*',
        'accept-language': 'vi',
        'priority': 'u=1, i',
        'referer': 'https://hunght1890.com/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
    }

    print(f"🔍 Đang tìm mã xác thực cho {mail_name}@simpace.edu.vn...")

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                try:
                    emails = response.json()
                    print(f"📧 Lần thử {attempt + 1}: Tìm thấy {len(emails)} email")

                    for email in emails:
                        subject = email.get('subject', '')
                        body = email.get('body', '')

                        # Kiểm tra email từ KlingAI
                        if 'klingai' in subject.lower() or 'kling' in subject.lower():
                            print(f"✅ Tìm thấy email từ KlingAI: {subject}")

                            # Extract mã xác thực 6 số
                            code_match = re.search(r'\b\d{6}\b', body)
                            if code_match:
                                code = code_match.group()
                                print(f"🔑 Mã xác thực: {code}")
                                return code
                            else:
                                print("❌ Không tìm thấy mã 6 số trong email")

                    if len(emails) == 0:
                        print(f"⏳ Chưa có email nào. Đợi {wait_time}s...")
                        time.sleep(wait_time)
                    else:
                        print("❌ Không tìm thấy email từ KlingAI")

                except json.JSONDecodeError:
                    print(f"❌ Lỗi parse JSON: {response.text[:100]}...")
            else:
                print(f"❌ HTTP Error {response.status_code}")

        except Exception as e:
            print(f"❌ Lỗi kết nối: {str(e)}")

        if attempt < max_retries - 1:
            print(f"⏳ Thử lại sau {wait_time}s...")
            time.sleep(wait_time)

    print("❌ Không thể lấy mã xác thực sau nhiều lần thử")
    return None

def get_code(email):
    url = "https://id.klingai.com/pass/ksi18n/web/email/code?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR85WymFzHoNZcUWNcTiC8KvwzS6UWycXZhGA6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UjA8hnlVOU.$HE_f2ed751012bfb45f292eb827258364dd73b8b9b9b9b870211503a967cd0a55321ae12eb822ef8763c2ef8751b9&caver=2"

    # URL encode email
    encoded_email = quote(email, safe='')
    payload = f'sid=ksi18n.ai.portal&email={encoded_email}&type=439&language=en&isWebSig4=true'
    headers = {
    'accept': '*/*',
    'accept-language': 'vi',
    'content-type': 'application/x-www-form-urlencoded',
    'fe-id': '4d0021ac1750482685583251',
    'fe-kpf': 'PC',
    'fe-kpn': 'KLING',
    'origin': 'https://app.klingai.com',
    'priority': 'u=1, i',
    'referer': 'https://app.klingai.com/',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
    'Cookie': 'ak_bmsc=9B1A08CE4D6C0E9337D54FC11B7E1C58~000000000000000000000000000000~YAAQGhBFdj5meGGXAQAAIgjmkBxpEEWVrPbinCokUeazgDwy0/nTFqm0F6aQbSrciaQSp11nZxhfputuCEwsm1XRyn+5Tnav+UYsd0/2ztRDbB6dBq2jePbPYY2qXkHYS840vuoGJfkp+2e57p1l9Gm5R/Umig+sjUkd6T2ZTzivrOn4bhemAlFPhOTOfRRSbDq7rkACyO49SY3nb79k6ShpjDZR1ivHQMMZLfju507378/WfCEjRSPQVxpuAwCVDb50/2ehlZCeVX4qVYPNa984APAxdoJb9eDRvioCVH1nx2ApwZW0JxpGm36RTYsozguW3RoXyCy3QUwSm1+XJdBQdQIq2hIXBXweFqYKA67BZ62R9P2uCUssAWVRr9FTQoPKDdEk+L1SOw==; weblogger_did=web_965476062C9F4920; __risk_web_device_id=4d0021ac1750482685583251; KLING_LAST_ACCESS_REGION=global; _did=web_9991773212581C17; did=web_065d429e07e6d37c8e665ece29c92d5f311a; _gcl_gs=2.1.k1$i1750482683$u73407682; _gcl_au=1.1.1153020380.1750482687; _gcl_aw=GCL.**********.EAIaIQobChMIiZGtk-CBjgMVPmwPAh1osw6JEAAYASAAEgKeZvD_BwE; _ga=GA1.1.5780344.**********; _ga_MWG30LDQKZ=GS2.1.s1750482688$o1$g0$t**********$j59$l0$h898407884; _uetsid=310447204e5e11f08cb5670e0c3321cd; _uetvid=310452b04e5e11f09552fbbb7600d754; _clck=tu4c88%7C2%7Cfwy%7C0%7C1998; _clsk=nixp9g%7C1750482692842%7C1%7C0%7Cz.clarity.ms%2Fcollect; bm_sv=5A72AE40D7221918CEBE299237442B47~YAAQGhBFdp9weGGXAQAAWVvpkByI/K55SZcb7DWb8flptn5cMt+e1kJqXVFdE4FwUF7XlxI1EksUCO2ae7FGTvmwocCT+VUsbIkxWgBL8FsTnBkH1wsOHk9ak3Mk5ev/58ljl+07mJM8MZeGdwiE0u4jLJu6WjgBcd8s8CnD+QadUGOIWIR7nPi70QxLAOYhknAnjyhcwJNnNsqJG4OHhBok4fiWHv+g5mxdqbZ4XQbqsBI9ba4Pz/CIicFUQW85JAQ=~1; bm_sv=5A72AE40D7221918CEBE299237442B47~YAAQGhBFdsN+eGGXAQAA3L7ukByFXQWlkufm7hoizZdb4eETxMeFrcn0ZYWBhdn7ycJk4vqLIxYBqjDl2DVJFIuucxcLSEeUS89fSIN6vyg9ymhQKDxzLwZJnJcxG9S4Co9y8mMNDkrYGC83UUyO4hqCHO2/YsIntGyKI64ogexl4x8Pl/Zkm/MKes4ik5gRayJn5N2uGvaAJwusnkxmLQK013ykQ6Io3x6Wbc+lo2JBawrQ8fRuCrusYfHsV6gQhj0=~1'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)

def register(email, password, code):
    url = "https://id.klingai.com/pass/ksi18n/web/register/emailPassword?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR845y2FzHoNfcUWNcTiC8KvwzS6UWycXZhGB6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UnA8hnlVOU.$HE_c7d84025278a816a1c1b8df76e0a21ae988d8c8c8c8d441420369905aa3e21dc2fd41b8d17dab256f7dab2648c&caver=2"

    # URL encode email and password
    encoded_email = quote(email, safe='')
    encoded_password = quote(password, safe='')
    payload = f'sid=ksi18n.ai.portal&email={encoded_email}&emailCode={code}&password={encoded_password}&setCookie=true&language=en&isWebSig4=true'
    headers = {
    'accept': '*/*',
    'accept-language': 'vi',
    'content-type': 'application/x-www-form-urlencoded',
    'fe-id': '4d0021ac1750482685583251',
    'fe-kpf': 'PC',
    'fe-kpn': 'KLING',
    'origin': 'https://app.klingai.com',
    'priority': 'u=1, i',
    'referer': 'https://app.klingai.com/',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
    'Cookie': 'ak_bmsc=9B1A08CE4D6C0E9337D54FC11B7E1C58~000000000000000000000000000000~YAAQGhBFdj5meGGXAQAAIgjmkBxpEEWVrPbinCokUeazgDwy0/nTFqm0F6aQbSrciaQSp11nZxhfputuCEwsm1XRyn+5Tnav+UYsd0/2ztRDbB6dBq2jePbPYY2qXkHYS840vuoGJfkp+2e57p1l9Gm5R/Umig+sjUkd6T2ZTzivrOn4bhemAlFPhOTOfRRSbDq7rkACyO49SY3nb79k6ShpjDZR1ivHQMMZLfju507378/WfCEjRSPQVxpuAwCVDb50/2ehlZCeVX4qVYPNa984APAxdoJb9eDRvioCVH1nx2ApwZW0JxpGm36RTYsozguW3RoXyCy3QUwSm1+XJdBQdQIq2hIXBXweFqYKA67BZ62R9P2uCUssAWVRr9FTQoPKDdEk+L1SOw==; weblogger_did=web_965476062C9F4920; __risk_web_device_id=4d0021ac1750482685583251; KLING_LAST_ACCESS_REGION=global; _did=web_9991773212581C17; did=web_065d429e07e6d37c8e665ece29c92d5f311a; _gcl_gs=2.1.k1$i1750482683$u73407682; _gcl_au=1.1.1153020380.1750482687; _gcl_aw=GCL.**********.EAIaIQobChMIiZGtk-CBjgMVPmwPAh1osw6JEAAYASAAEgKeZvD_BwE; _ga=GA1.1.5780344.**********; _ga_MWG30LDQKZ=GS2.1.s1750482688$o1$g0$t**********$j59$l0$h898407884; _uetsid=310447204e5e11f08cb5670e0c3321cd; _uetvid=310452b04e5e11f09552fbbb7600d754; _clck=tu4c88%7C2%7Cfwy%7C0%7C1998; _clsk=nixp9g%7C1750482692842%7C1%7C0%7Cz.clarity.ms%2Fcollect; bm_sv=5A72AE40D7221918CEBE299237442B47~YAAQGhBFdqFweGGXAQAA+1vpkBxwmOjX2am18jgzfy6T9CpXZMb9Cz1Ms3spzKXBvSHZfgSVdkhasQRTyc8+6Ykjv1SEZupy3zItoEG59TPwlzr2pcltJnH1JzNR9OZSY2L68u41EpXEgXFmzYRBzTBFbY0zPWZBCZ4CVk5PiIg0di4vt9rSZatoH5T/7Mg0m2UcY6MkhOk0h07qz0RrPF7YBf234s7PaUuWlnqMSfkaS9zb+G35C1oc5Vy9bLed8HQ=~1; bm_sv=5A72AE40D7221918CEBE299237442B47~YAAQHBBFdgqumniXAQAAHQfwkBwf4W5nbYZuyAmMlTz/3deolAXFO9Zg3Hht3jfPMGOvi5avG+dxV+GHk19bcp3V6ib4JG991fBaFrP9UftPjH3SDCP3KT+VxwiC6Secz6OOLV+hgkkH/dRkF6hsL5bUxX3hDGLWabG3PbctDc+zF55AoVRHZL3BFP03GYqQzPV6Gdf+uf0aD1zjv9sIps2EboCQbYPHCChB1k5OGXSCKTshUWHW6/oszji408xhVxE=~1; userId=33806279; passToken=ChNwYXNzcG9ydC5wYXNzLXRva2VuEsABwqtJTpYx2lNJYWzRSqhRH8Cu6UkqccKN1CLrH8w-ODmo_ywcmowSvyhbNvNHUDvQwJsHq3sNKDbi-MAf7Os0uBwijHMuzpCr5_JwbbwMLbale1ddRtin55veHCgD3TXsO4yoKhZTimIQ3ILKmRJkDDy4XxanB6GlFEG12UxYWEeJUUn3Svu3HSJ9ylxNHX1yMXd3UD_kN1n8GQQ8Gvucviek4Qr1WARxkUw1EcnlNmIU1J_ZF11_IoOOZ12p55nqGhIdM2LIEOHfbyXdkao_TlxguBEiIDb0RPvR0KUTMcfM3LKlUuQ809igtDGNQndeXf-JYPAEKAUwAQ; userId=33806279'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)

def test_with_existing_email():
    """Test với email có sẵn (123123abvvv)"""
    mail_name = "123123abvvv"
    email = f"{mail_name}@simpace.edu.vn"

    print(f"🧪 Test với email có sẵn: {email}")

    # Lấy mã xác thực từ tempmail API
    print(f"\n🔍 Đang lấy mã xác thực từ tempmail...")
    verification_code = get_verification_code_from_tempmail(mail_name, max_retries=3, wait_time=2)

    if verification_code:
        print(f"✅ Lấy mã xác thực thành công: {verification_code}")
        return verification_code
    else:
        print("❌ Không thể lấy mã xác thực.")
        return None

def main():
    """Workflow đúng: Random email -> Đăng ký Kling (gửi code) -> Lấy code từ tempmail API"""
    print("🚀 Bắt đầu tạo tài khoản Kling AI...")
    print("📝 Workflow: Random Email → Đăng ký Kling → Lấy Code từ Tempmail API")

    # Bước 1: Tạo random email
    print("\n📧 Bước 1: Tạo random email...")
    email, mail_name = generate_random_email()
    print(f"✅ Email: {email}")
    print(f"📋 Mail name: {mail_name}")

    # Bước 2: Gửi yêu cầu mã xác thực đến Kling AI (email sẽ được gửi)
    print(f"\n📨 Bước 2: Gửi yêu cầu mã xác thực đến {email}...")
    get_code(email)

    print("✅ Yêu cầu đã được gửi! Email verification sẽ được gửi đến tempmail.")

    # Bước 3: Đợi một chút rồi lấy mã xác thực từ tempmail API
    print(f"\n🔍 Bước 3: Lấy mã xác thực từ tempmail API...")
    print("⏳ Đợi 10 giây để email được gửi...")
    time.sleep(10)  # Đợi email được gửi

    verification_code = get_verification_code_from_tempmail(mail_name, max_retries=10, wait_time=5)

    if verification_code:
        print(f"✅ Lấy mã xác thực thành công: {verification_code}")

        # Bước 4: Đăng ký tài khoản
        password = input("\n🔐 Nhập password để đăng ký: ").strip()
        if password:
            print(f"\n📝 Bước 4: Đăng ký tài khoản với email: {email}")
            register(email, password, verification_code)
            print(f"\n✅ Hoàn thành! Tài khoản: {email} | Password: {password}")
        else:
            print("❌ Cần nhập password để hoàn thành đăng ký")
    else:
        print("❌ Không thể lấy mã xác thực từ tempmail API.")
        print("💡 Có thể thử lại sau hoặc kiểm tra email manually.")

if __name__ == "__main__":
    main()
