import requests
import os
import json
from typing import Optional, Dict, Any

class KlingAI:
    """
    Kling AI Video Generation Tool
    
    Workflow:
    1. generate_token() -> Create upload token
    2. upload_image() -> Upload image file in fragments
    3. complete_upload() -> Finalize upload
    4. get_image_url() -> Get uploaded image URL
    5. submit_video_creating() -> Create video with prompt
    """
    
    def __init__(self, cookie: str):
        """
        Initialize KlingAI with authentication cookie

        Args:
            cookie (str): Authentication cookie for Kling AI
        """
        self.cookie = cookie
        self.base_headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en',
            'origin': 'https://app.klingai.com',
            'priority': 'u=1, i',
            'referer': 'https://app.klingai.com/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'time-zone': 'Asia/Saigon',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
            'Cookie': self.cookie
        }

    def validate_cookie(self) -> bool:
        """
        Validate if cookie is still active by testing API call

        Returns:
            bool: True if cookie is valid, False otherwise
        """
        try:
            print("🔍 Validating cookie...")
            # Test với API đơn giản
            test_url = "https://api-app-global.klingai.com/api/upload/issue/token?filename=test.png"
            response = requests.get(test_url, headers=self.base_headers, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if "data" in data and "token" in data["data"]:
                    print("✅ Cookie is valid")
                    return True
                else:
                    print("❌ Cookie invalid - No token in response")
                    return False
            elif response.status_code == 401:
                print("❌ Cookie expired - Authentication failed")
                return False
            else:
                print(f"⚠️ Cookie validation uncertain - Status: {response.status_code}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"❌ Cookie validation failed: {e}")
            return False

    def parse_cookie_info(self) -> Dict[str, Any]:
        """
        Parse cookie để lấy thông tin expiration và session

        Returns:
            dict: Thông tin về cookie
        """
        import time
        from datetime import datetime

        cookie_info = {
            "total_cookies": 0,
            "session_cookies": [],
            "expired_cookies": [],
            "valid_cookies": [],
            "critical_cookies": []
        }

        # Parse cookie string
        cookies = self.cookie.split(';')
        cookie_info["total_cookies"] = len(cookies)

        current_time = int(time.time())

        for cookie in cookies:
            cookie = cookie.strip()
            if '=' in cookie:
                name = cookie.split('=')[0].strip()

                # Check for critical cookies
                critical_names = ['userId', 'ksi18n.ai.portal_st', 'ak_bmsc', 'bm_sv', '_did', 'did']
                if any(critical in name for critical in critical_names):
                    cookie_info["critical_cookies"].append(name)

                # Check for timestamp in cookie value
                if '$i' in cookie:  # Google Analytics timestamp format
                    try:
                        timestamp_part = cookie.split('$i')[1].split('$')[0]
                        timestamp = int(timestamp_part)
                        if timestamp < current_time:
                            cookie_info["expired_cookies"].append(name)
                        else:
                            cookie_info["valid_cookies"].append(name)
                    except:
                        cookie_info["session_cookies"].append(name)
                else:
                    cookie_info["session_cookies"].append(name)

        return cookie_info
    
    def generate_token(self, filename: str = "image.png") -> Optional[str]:
        """
        Generate upload token for file upload
        
        Args:
            filename (str): Name of the file to upload
            
        Returns:
            str: Upload token or None if failed
        """
        url = f"https://api-app-global.klingai.com/api/upload/issue/token?filename={filename}"
        
        try:
            response = requests.get(url, headers=self.base_headers)
            response.raise_for_status()
            return response.json()["data"]["token"]
        except requests.exceptions.RequestException as e:
            print(f"❌ Failed to generate token: {e}")
            return None
        except KeyError as e:
            print(f"❌ Invalid response format: {e}")
            return None
    
    def upload_fragment(self, fragment_data: bytes, upload_token: str, 
                       fragment_id: int, total_size: int, start_byte: int) -> Optional[requests.Response]:
        """
        Upload a single fragment to server
        
        Args:
            fragment_data (bytes): Fragment data
            upload_token (str): Upload token
            fragment_id (int): Fragment ID (0, 1, 2...)
            total_size (int): Total file size
            start_byte (int): Starting byte position
            
        Returns:
            Response object or None if failed
        """
        end_byte = start_byte + len(fragment_data) - 1
        url = f"https://az3-upload.uvfuns.com/api/upload/fragment?upload_token={upload_token}&fragment_id={fragment_id}"
        
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'vi',
            'content-range': f"bytes {start_byte}-{end_byte}/{total_size}",
            'content-type': 'application/octet-stream',
            'origin': 'https://app.klingai.com',
            'priority': 'u=1, i',
            'referer': 'https://app.klingai.com/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }
        
        try:
            response = requests.post(url, headers=headers, data=fragment_data)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            print(f"❌ Upload fragment {fragment_id} failed: {e}")
            return None
    
    def upload_image(self, file_path: str, upload_token: str) -> int:
        """
        Split file into fragments and upload to server
        
        Args:
            file_path (str): Path to image file
            upload_token (str): Upload token
            
        Returns:
            int: Number of fragments uploaded successfully
        """
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return 0
            
        fragment_size = 1024 * 1024  # 1MB
        total_size = os.path.getsize(file_path)
        fragment_count = 0
        
        print(f"📁 Uploading file: {file_path} ({total_size} bytes)")
        
        with open(file_path, 'rb') as f:
            fragment_id = 0
            start_byte = 0
            
            while True:
                chunk = f.read(fragment_size)
                if not chunk:
                    break
                
                print(f"⬆️ Uploading fragment {fragment_id} (bytes {start_byte}-{start_byte + len(chunk) - 1})...")
                
                response = self.upload_fragment(
                    fragment_data=chunk,
                    upload_token=upload_token,
                    fragment_id=fragment_id,
                    total_size=total_size,
                    start_byte=start_byte
                )
                
                if response and response.status_code == 200:
                    print(f"✅ Fragment {fragment_id} uploaded successfully")
                    fragment_count += 1
                else:
                    print(f"❌ Failed to upload fragment {fragment_id}")
                    break  # Stop on first failure
                
                fragment_id += 1
                start_byte += len(chunk)
        
        print(f"📊 Upload completed: {fragment_count} fragments uploaded")
        return fragment_count
    
    def complete_upload(self, upload_token: str, fragment_count: int) -> bool:
        """
        Complete the upload process
        
        Args:
            upload_token (str): Upload token
            fragment_count (int): Number of fragments uploaded
            
        Returns:
            bool: True if successful, False otherwise
        """
        url = f"https://az3-upload.uvfuns.com/api/upload/complete?fragment_count={fragment_count}&upload_token={upload_token}"
        
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'vi',
            'content-length': '0',
            'origin': 'https://app.klingai.com',
            'priority': 'u=1, i',
            'referer': 'https://app.klingai.com/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'cross-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }
        
        try:
            response = requests.post(url, headers=headers, data={})
            response.raise_for_status()
            print(f"✅ Upload completed: {response.text}")
            return True
        except requests.exceptions.RequestException as e:
            print(f"❌ Failed to complete upload: {e}")
            return False
    
    def get_image_url(self, upload_token: str) -> Optional[str]:
        """
        Get the URL of uploaded image
        
        Args:
            upload_token (str): Upload token
            
        Returns:
            str: Image URL or None if failed
        """
        url = f"https://api-app-global.klingai.com/api/upload/verify/token?token={upload_token}"
        
        try:
            response = requests.get(url, headers=self.base_headers)
            response.raise_for_status()
            data = response.json()
            
            if "data" in data and "url" in data["data"]:
                image_url = data["data"]["url"]
                print(f"🖼️ Image URL: {image_url}")
                return image_url
            else:
                print(f"❌ Invalid response format: {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Failed to get image URL: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON response: {e}")
            return None

    def submit_video_creating(self, prompt: str, image_url: str) -> Optional[Dict[str, Any]]:
        """
        Submit video creation request

        Args:
            prompt (str): Text prompt for video generation
            image_url (str): URL of the uploaded image

        Returns:
            dict: Response data or None if failed
        """
        # API endpoint thực tế từ curl command
        url = "https://api-app-global.klingai.com/api/task/submit"

        # Query parameters từ curl
        params = {
            "__NS_hxfalcon": "HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlzbSoJ4oP1qJUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR87uyWFzHoNWcUWNcTiC8KvwzS6RWycXZhFn6tQ1i48QMwoXBL9qinQV6ZopOnzfHReHR73UY4wsnqSzL-K_LBD6VO_tENJbOjbfQDxQZeqKf-V0P1F6KSOhtPeoSvJjvGNuq6oLJF7N--qFxX4qrfsRPsxUYGcl31Csc-HtZLTgtgpPduy7lQ0dHT1t0a_A8hnlVOU.%24HE_b9a63e5b5926bb626165f359606806768af3f2f2f2f3dc6a5e48e82f8542b3ac20a965f369a4cc2889a4cc1af2",
            "caver": "2"
        }

        # Payload theo định dạng curl
        payload = {
            "type": "m2v_img2video",
            "arguments": [
                {
                    "name": "prompt",
                    "value": prompt  # Sử dụng prompt từ parameter
                },
                {
                    "name": "negative_prompt",
                    "value": ""
                },
                {
                    "name": "duration",
                    "value": "5"
                },
                {
                    "name": "imageCount",
                    "value": "1"
                },
                {
                    "name": "kling_version",
                    "value": "1.6"
                },
                {
                    "name": "cfg",
                    "value": "0.5"
                },
                {
                    "name": "camera_json",
                    "value": "{\"type\":\"empty\",\"horizontal\":0,\"vertical\":0,\"zoom\":0,\"tilt\":0,\"pan\":0,\"roll\":0}"
                },
                {
                    "name": "camera_control_enabled",
                    "value": "false"
                },
                {
                    "name": "biz",
                    "value": "klingai"
                }
            ],
            "inputs": [
                {
                    "inputType": "URL",
                    "url": image_url,  # Sử dụng image_url từ parameter
                    "name": "input"
                }
            ]
        }

        # Headers theo curl command
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en',
            'content-type': 'application/json',
            'origin': 'https://app.klingai.com',
            'priority': 'u=1, i',
            'referer': 'https://app.klingai.com/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'time-zone': 'Asia/Saigon',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
            'Cookie': self.cookie  # Sử dụng cookie từ class
        }

        try:
            print(f"🎬 Submitting video creation request...")
            print(f"🖼️ Image URL: {image_url}")
            print(f"💭 Prompt: {prompt}")

            response = requests.post(url, headers=headers, params=params, json=payload)
            response.raise_for_status()
            data = response.json()

            print(f"✅ Video creation submitted successfully!")
            print(f"📋 Response: {data}")
            return data

        except requests.exceptions.RequestException as e:
            print(f"❌ Failed to submit video creation: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"📋 Response text: {e.response.text}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON response: {e}")
            return None

    def create_video_from_image(self, file_path: str, prompt: str) -> Optional[str]:
        """
        Complete workflow: Upload image and create video

        Args:
            file_path (str): Path to image file
            prompt (str): Text prompt for video generation

        Returns:
            str: Video creation result or None if failed
        """
        print("🚀 Starting Kling AI video creation workflow...")

        # Step 1: Generate upload token
        print("1️⃣ Generating upload token...")
        filename = os.path.basename(file_path)
        upload_token = self.generate_token(filename)
        if not upload_token:
            return None
        print(f"✅ Upload token generated: {upload_token[:50]}...")

        # Step 2: Upload image
        print("2️⃣ Uploading image...")
        fragment_count = self.upload_image(file_path, upload_token)
        if fragment_count == 0:
            return None

        # Step 3: Complete upload
        print("3️⃣ Completing upload...")
        if not self.complete_upload(upload_token, fragment_count):
            return None

        # Step 4: Get image URL
        print("4️⃣ Getting image URL...")
        image_url = self.get_image_url(upload_token)
        if not image_url:
            return None

        # Step 5: Submit video creation
        print("5️⃣ Submitting video creation...")
        result = self.submit_video_creating(prompt, image_url)

        if result:
            print("🎉 Video creation workflow completed successfully!")
            return result
        else:
            print("❌ Video creation workflow failed")
            return None


# Example usage
if __name__ == "__main__":
    # Example configuration - replace with your actual values
    COOKIE = "your_cookie_here"
    IMAGE_PATH = "path/to/your/image.png"
    PROMPT = "Create a beautiful video from this image"

    # Create KlingAI instance
    kling = KlingAI(COOKIE)

    # Run the complete workflow
    result = kling.create_video_from_image(IMAGE_PATH, PROMPT)

    if result:
        print(f"Success: {result}")
    else:
        print("Failed to create video")
