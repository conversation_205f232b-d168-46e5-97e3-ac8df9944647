# 🔑 Hướng dẫn lấy Token Tempmail mới

## ❌ **Vấn đề hiện tại:**
Token mặc định đã hết quota (10 emails/ngày). Cần token mới để tiếp tục.

## ✅ **Cách lấy Token mới:**

### **Bước 1: Tạo tài khoản Tempmail**
1. <PERSON><PERSON><PERSON> cập: https://tempmail.id.vn
2. Click "Đăng ký" 
3. Nhập email và password
4. Xác nhận email đăng ký

### **Bước 2: Lấy Token API**
1. Đăng nhập vào tài khoản
2. Vào **Dashboard** > **API Settings**
3. Copy **API Token** (dạng: `1234|abcdef...`)
4. Paste vào ô "Token Tempmail" trong tool

### **Bước 3: Test Token**
1. Paste token vào tool
2. Click nút **"🧪 Test Token"**
3. <PERSON><PERSON><PERSON> thành công → <PERSON><PERSON> thể sử dụng
4. <PERSON><PERSON><PERSON> lỗi → Kiểm tra lại token

## 🔧 **Sử dụng trong Tool:**

### **GUI có sẵn:**
- ✅ **Ô nhập Token**: Paste token mới vào đây
- ✅ **Nút Test Token**: Kiểm tra token có hoạt động không
- ✅ **Tab Hướng dẫn**: Chi tiết cách sử dụng

### **Workflow:**
```
1. Lấy token mới từ tempmail.id.vn
   ↓
2. Paste vào ô "Token Tempmail"
   ↓
3. Click "🧪 Test Token"
   ↓
4. Nếu OK → Bắt đầu đăng ký
   ↓
5. Tool sẽ tạo accounts tự động
```

## 💡 **Tips:**

### **Khi token hết quota:**
- Tạo account tempmail mới
- Lấy token mới
- Thay thế trong tool

### **Backup tokens:**
- Tạo nhiều accounts tempmail
- Lưu tokens để backup
- Khi hết quota → chuyển token khác

### **Giới hạn:**
- **10 emails/token/ngày**
- **Free** - không cần trả phí
- **Unlimited accounts** - chỉ cần token mới

## 🚨 **Troubleshooting:**

### **Lỗi 400 "giới hạn email":**
```
Nguyên nhân: Token hết quota
Giải pháp: Lấy token mới
```

### **Lỗi 401 "unauthorized":**
```
Nguyên nhân: Token không hợp lệ
Giải pháp: Kiểm tra lại token
```

### **Test Token thất bại:**
```
1. Kiểm tra internet
2. Kiểm tra token đúng format
3. Đăng nhập lại tempmail.id.vn
4. Copy token mới
```

## 📞 **Hỗ trợ:**

Nếu vẫn gặp vấn đề:
1. Check tab "📖 Hướng dẫn" trong tool
2. Test token trước khi dùng
3. Đảm bảo token format đúng: `số|chữ_cái_số`

**Tool đã được cập nhật để support token input!** 🎉
