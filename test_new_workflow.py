#!/usr/bin/env python3
"""
Test script để kiểm tra workflow mới
"""

import requests
from urllib.parse import quote
import random
import string
import json
import re
import time

def generate_random_email():
    """Tạo random email name (10-14 ký tự) với domain simpace.edu.vn"""
    username_length = random.randint(10, 14)
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=username_length))
    email = f"{username}@simpace.edu.vn"
    return email, username

def get_code(email):
    """G<PERSON>i yêu cầu mã xác thực"""
    url = "https://id.klingai.com/pass/ksi18n/web/email/code?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR85WymFzHoNZcUWNcTiC8KvwzS6UWycXZhGA6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UjA8hnlVOU.$HE_f2ed751012bfb45f292eb827258364dd73b8b9b9b9b870211503a967cd0a55321ae12eb822ef8763c2ef8751b9&caver=2"
    
    encoded_email = quote(email, safe='')
    payload = f'sid=ksi18n.ai.portal&email={encoded_email}&type=439&language=en&isWebSig4=true'
    headers = {
        'accept': '*/*',
        'accept-language': 'vi',
        'content-type': 'application/x-www-form-urlencoded',
        'fe-id': '4d0021ac1750482685583251',
        'fe-kpf': 'PC',
        'fe-kpn': 'KLING',
        'origin': 'https://app.klingai.com',
        'priority': 'u=1, i',
        'referer': 'https://app.klingai.com/',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
    }
    
    try:
        response = requests.post(url, headers=headers, data=payload, timeout=10)
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Lỗi gửi mã: {str(e)}")
        return False

def get_verification_code_from_tempmail(mail_name, max_retries=5, wait_time=5):
    """Lấy mã xác thực từ tempmail sử dụng API hunght1890.com"""
    url = f"https://hunght1890.com/{mail_name}%40simpace.edu.vn"
    
    headers = {
        'accept': '*/*',
        'accept-language': 'vi',
        'priority': 'u=1, i',
        'referer': 'https://hunght1890.com/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
    }
    
    print(f"🔍 Đang tìm mã xác thực cho {mail_name}@simpace.edu.vn...")
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                try:
                    emails = response.json()
                    print(f"📧 Lần thử {attempt + 1}: Tìm thấy {len(emails)} email")
                    
                    for email in emails:
                        subject = email.get('subject', '')
                        body = email.get('body', '')
                        
                        # Kiểm tra email từ KlingAI
                        if 'klingai' in subject.lower() or 'kling' in subject.lower():
                            print(f"✅ Tìm thấy email từ KlingAI: {subject}")
                            
                            # Extract mã xác thực 6 số
                            code_match = re.search(r'\b\d{6}\b', body)
                            if code_match:
                                code = code_match.group()
                                print(f"🔑 Mã xác thực: {code}")
                                return code
                            else:
                                print("❌ Không tìm thấy mã 6 số trong email")
                    
                    if len(emails) == 0:
                        print(f"⏳ Chưa có email nào. Đợi {wait_time}s...")
                        time.sleep(wait_time)
                    else:
                        print("❌ Không tìm thấy email từ KlingAI")
                        
                except json.JSONDecodeError:
                    print(f"❌ Lỗi parse JSON: {response.text[:100]}...")
            else:
                print(f"❌ HTTP Error {response.status_code}")
                
        except Exception as e:
            print(f"❌ Lỗi kết nối: {str(e)}")
            
        if attempt < max_retries - 1:
            print(f"⏳ Thử lại sau {wait_time}s...")
            time.sleep(wait_time)
    
    print("❌ Không thể lấy mã xác thực sau nhiều lần thử")
    return None

def test_workflow():
    """Test workflow mới"""
    print("🧪 Testing New Workflow...")
    
    # Bước 1: Tạo random email
    print("\n📧 Bước 1: Tạo random email...")
    email, mail_name = generate_random_email()
    print(f"✅ Email: {email}")
    print(f"📋 Mail name: {mail_name}")

    # Bước 2: Gửi yêu cầu mã xác thực đến Kling AI
    print(f"\n📨 Bước 2: Gửi yêu cầu mã xác thực đến {email}...")
    if get_code(email):
        print("✅ Yêu cầu đã được gửi!")
        
        # Bước 3: Đợi một chút rồi lấy mã xác thực từ tempmail API
        print(f"\n🔍 Bước 3: Lấy mã xác thực từ tempmail API...")
        print("⏳ Đợi 10 giây để email được gửi...")
        time.sleep(10)
        
        verification_code = get_verification_code_from_tempmail(mail_name)
        
        if verification_code:
            print(f"✅ Lấy mã xác thực thành công: {verification_code}")
            print(f"🎉 Workflow hoàn thành! Email: {email}, Code: {verification_code}")
        else:
            print("❌ Không thể lấy mã xác thực từ tempmail API.")
    else:
        print("❌ Không thể gửi yêu cầu mã xác thực")

if __name__ == "__main__":
    test_workflow()
