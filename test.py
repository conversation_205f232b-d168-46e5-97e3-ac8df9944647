import requests
import os
import json 

def generate_token():
    url = "https://api-app-global.klingai.com/api/upload/issue/token?filename=test.png"

    payload = {}
    headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en',
    'origin': 'https://app.klingai.com',
    'priority': 'u=1, i',
    'referer': 'https://app.klingai.com/',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'time-zone': 'Asia/Saigon',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON>HT<PERSON>, like Gecko) Chrome/137.0.0.0 Safari/537.36',
    'Cookie': 'ak_bmsc=83C27119F0F6AC51E18B29097CF8A39A~000000000000000000000000000000~YAAQHBBFduGEn3iXAQAAD8rTkhznPAsb6qccUQZAuy+zMRwczQ5RfOT65DXRNKqQ/6LlqT4Z4xB5J1nUamkkO4M5O9YSCgY6M8ZIi35aWWSs2WYb0fQEvgmS8Qh5ASTZ1SiKVIAYsmvMMzhJcDUGQZMs3l3nsQf/JId5Gy3ORflx+AV83KwgnFLEKZIMyFETDQvzfcE4Mt9sx1+Zz9OBf2lz2zBzMBK8+GAArZEDVEj7TstjsOL4NX8kPwp9TlXiqFgN+tt6k8ghEdqDqHk0h7MVjRt5HO2MhwDrMbgkjmaQBlH4rAJ6MfiFTd4zKJflUc09b3yLyiQfyi2AL2Pd7p3mIjzBhNOC3aT2nUYSvOX+/XW5r1RXGoHJqLI90ntl9879/ggwWjLWDg==; weblogger_did=web_632115628CB1F1BB; __risk_web_device_id=e68f044f1750515044401041; KLING_LAST_ACCESS_REGION=global; _did=web_92130551984D11C3; did=web_4a7e5bf68b97d26ce1d2feeaec7e972ce3e4; _gcl_gs=2.1.k1$i1750515043$u73407682; _gcl_au=1.1.381305734.1750515045; _gcl_aw=GCL.1750515046.EAIaIQobChMIu-CB2NiCjgMVY-sWBR1h-CGjEAAYASAAEgIQr_D_BwE; _ga=GA1.1.1032914907.1750515046; _clck=1asel37%7C2%7Cfwy%7C0%7C1998; _clsk=1j4fxyb%7C1750515080261%7C1%7C0%7Cz.clarity.ms%2Fcollect; userId=33852819; ksi18n.ai.portal_st=ChNrc2kxOG4uYWkucG9ydGFsLnN0EqABAGWKE5aUtsm2AxEcjzIYdsr1DBYXjuAYiRVgFa0C3ng2Cq7LaRRPKs47kHjX7MAnOGzKJy-8zm-c50shfFbj9tCZzPUkvIejCfRnL9g6ALXGPD9oiHtChkAs-Kyv1oX0kghDlZtYBqDopxtzHFMi4XNVFimAyP8AlQiRSDhlr35RSUh4jcV0ioqBV1T9OiofbMfFhgn9-NXHaJriihJuKBoSvToad4M-33jzHbN45OfoeYsEIiBWcUZRV-f_d-yg27c4c-keZoMZUhqNitSZVBz1tBDWfigFMAE; ksi18n.ai.portal_ph=e6923ef34e0aea96b431d4323dc705c71d65; _uetsid=8666b7e04ea911f0a8cf379f19c297ab; _uetvid=8666ce504ea911f0bddd95635a1534c4; _ga_MWG30LDQKZ=GS2.1.s1750518627$o2$g1$t1750518633$j54$l0$h1900269463; bm_sv=90C45B951B8F09536F8F92338C56AC2F~YAAQGhBFdmBDfmGXAQAAldINkxxiQ0h1rseBt6paTueRZW6+HllgyAQqL0LdWKBKOenBuEwprbO+2Rafmi1Bxk9CqaTLnJDxaqS7RfwToVFzFl+CHqdYkqDcAchaEus2YNmXVGZqlI108vekVk2cTj2mIRw+DKg2oPJ3t91Ern5cYL4LVZWL3sM8YDR4cP0si6IDzPIZkBEP1UUNmQ1oJolcxpyc5/H14eVFij59a9E5YAe9o6xaX1RnIrx0WBH3FF4=~1; userId=33806279'
    }

    response = requests.get(url, headers=headers)
    return response.json()["data"]["token"]
print(generate_token())


def upload_fragment(fragment_data, upload_token, fragment_id, total_size, start_byte):
    """
    Gửi một fragment lên server theo đúng định dạng curl yêu cầu.

    Args:
        fragment_data (bytes): Dữ liệu fragment.
        upload_token (str): Token tải lên.
        fragment_id (int): ID của fragment (ví dụ: 0, 1, 2...).
        total_size (int): Tổng kích thước file gốc.
        start_byte (int): Byte bắt đầu của fragment trong file gốc.

    Returns:
        Response object hoặc None nếu lỗi.
    """
    end_byte = start_byte + len(fragment_data) - 1
    url = f"https://az3-upload.uvfuns.com/api/upload/fragment?upload_token={upload_token}&fragment_id={fragment_id}"

    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'vi',
        'content-range': f"bytes {start_byte}-{end_byte}/{total_size}",
        'content-type': 'application/octet-stream',
        'origin': 'https://app.klingai.com',
        'priority': 'u=1, i',
        'referer': 'https://app.klingai.com/',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'cross-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
    }

    try:
        response = requests.post(url, headers=headers, data=fragment_data)
        response.raise_for_status()
        return response
    except requests.exceptions.RequestException as e:
        print(f"❌ Upload fragment {fragment_id} failed: {e}")
        return None

def split_and_upload(file_path, upload_token):
    """
    Chia nhỏ file và upload từng fragment lên server.

    Args:
        file_path (str): Đường dẫn file cần upload.
        upload_token (str): Token tải lên.

    Returns:
        int: Số lượng fragment đã upload.
    """
    fragment_size = 1024 * 1024  # 1MB
    total_size = os.path.getsize(file_path)

    fragment_count = 0

    with open(file_path, 'rb') as f:
        fragment_id = 0
        start_byte = 0

        while True:
            chunk = f.read(fragment_size)
            if not chunk:
                break

            print(f"⬆️ Uploading fragment {fragment_id} (bytes {start_byte}-{start_byte + len(chunk) - 1})...")

            response = upload_fragment(
                fragment_data=chunk,
                upload_token=upload_token,
                fragment_id=fragment_id,
                total_size=total_size,
                start_byte=start_byte
            )

            if response and response.status_code == 200:
                print(f"✅ Fragment {fragment_id} uploaded. Status: {response.status_code}")
                fragment_count += 1
            else:
                print(f"❌ Failed to upload fragment {fragment_id}")
                # Nếu cần dừng tại fragment lỗi, bỏ comment dòng dưới
                # break

            fragment_id += 1
            start_byte += len(chunk)

    return fragment_count


def complete_upload(upload_token,count):

    url = "https://az3-upload.uvfuns.com/api/upload/complete?fragment_count="+count+"&upload_token=" + upload_token

    payload = {}
    headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'vi',
    'content-length': '0',
    'origin': 'https://app.klingai.com',
    'priority': 'u=1, i',
    'referer': 'https://app.klingai.com/',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'cross-site',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print("complete: " + response.text)

def get_link(upload_token):
    import requests

    url = "https://api-app-global.klingai.com/api/upload/verify/token?token=" + upload_token

    payload = {}
    headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en',
    'origin': 'https://app.klingai.com',
    'priority': 'u=1, i',
    'referer': 'https://app.klingai.com/',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'time-zone': 'Asia/Saigon',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
    'Cookie': 'ak_bmsc=83C27119F0F6AC51E18B29097CF8A39A~000000000000000000000000000000~YAAQHBBFduGEn3iXAQAAD8rTkhznPAsb6qccUQZAuy+zMRwczQ5RfOT65DXRNKqQ/6LlqT4Z4xB5J1nUamkkO4M5O9YSCgY6M8ZIi35aWWSs2WYb0fQEvgmS8Qh5ASTZ1SiKVIAYsmvMMzhJcDUGQZMs3l3nsQf/JId5Gy3ORflx+AV83KwgnFLEKZIMyFETDQvzfcE4Mt9sx1+Zz9OBf2lz2zBzMBK8+GAArZEDVEj7TstjsOL4NX8kPwp9TlXiqFgN+tt6k8ghEdqDqHk0h7MVjRt5HO2MhwDrMbgkjmaQBlH4rAJ6MfiFTd4zKJflUc09b3yLyiQfyi2AL2Pd7p3mIjzBhNOC3aT2nUYSvOX+/XW5r1RXGoHJqLI90ntl9879/ggwWjLWDg==; weblogger_did=web_632115628CB1F1BB; __risk_web_device_id=e68f044f1750515044401041; KLING_LAST_ACCESS_REGION=global; _did=web_92130551984D11C3; did=web_4a7e5bf68b97d26ce1d2feeaec7e972ce3e4; _gcl_gs=2.1.k1$i1750515043$u73407682; _gcl_au=1.1.381305734.1750515045; _gcl_aw=GCL.1750515046.EAIaIQobChMIu-CB2NiCjgMVY-sWBR1h-CGjEAAYASAAEgIQr_D_BwE; _ga=GA1.1.1032914907.1750515046; _clck=1asel37%7C2%7Cfwy%7C0%7C1998; userId=33852819; ksi18n.ai.portal_st=ChNrc2kxOG4uYWkucG9ydGFsLnN0EqABAGWKE5aUtsm2AxEcjzIYdsr1DBYXjuAYiRVgFa0C3ng2Cq7LaRRPKs47kHjX7MAnOGzKJy-8zm-c50shfFbj9tCZzPUkvIejCfRnL9g6ALXGPD9oiHtChkAs-Kyv1oX0kghDlZtYBqDopxtzHFMi4XNVFimAyP8AlQiRSDhlr35RSUh4jcV0ioqBV1T9OiofbMfFhgn9-NXHaJriihJuKBoSvToad4M-33jzHbN45OfoeYsEIiBWcUZRV-f_d-yg27c4c-keZoMZUhqNitSZVBz1tBDWfigFMAE; ksi18n.ai.portal_ph=e6923ef34e0aea96b431d4323dc705c71d65; _ga_MWG30LDQKZ=GS2.1.s1750518627$o2$g1$t1750521000$j58$l0$h1900269463; _uetsid=8666b7e04ea911f0a8cf379f19c297ab; _uetvid=8666ce504ea911f0bddd95635a1534c4; bm_sv=90C45B951B8F09536F8F92338C56AC2F~YAAQGhBFdhWSfmGXAQAAk1YvkxwuZLvXZJsmrsvhBJ+T+ro7SlJeZjQ/NK7/4wfJC0s85fRGIbI5I+UwDxFyAJF8d0aTX3SA194P++o3GzZulRXohZMkdz0H8PUPssT7TcKizTlI7ajwB3r00XlZ9zLiShKDQHXS7LOpvh+BuPh0+HNrjSvP8PeBa3smeXPt0Ehy4cWGAZzVrSJT1H2UF64Momv9fgnrsVV3LZ1SUfo/AvJlJHpxLUJ3kAz2LZG7bz61~1; userId=33806279'
    }

    response = requests.request("GET", url, headers=headers, data=payload)

    print(response.text)


upload_token = "Cg51cGxvYWRlci50b2tlbhLqAVPnVj3EUzc44DlF7r2MsOPStamecIeg6AE8hKo2liKanD7yI-1RUrn8lF9ThkwC7yfVADLZHzEB_4i8dNUtCJfrTjP-smhboAbP7yM0XzMqwFLt9Iebcb-tMmNKm5xFjV5YQnrHqYA6OWshioMRq7E1t9Fh_sWiIWIDuKoaFUQ8iUP1cx1hhIPRaiDIcgmJDIxyeEECX2LEcKO-ixjCwU2A7x-xfbcGENJKJnW7U9fV4I7vxAqE5e4DfLnsg4WId09Spw8Pm3AVhh-09rxXyQzs6xnHYt16bFsMdyp3nNbCWGJoa2rvVtXe4xoSgWJp7sSaDvf0AfhenxCNMICxKAUwAg"

file_path = "C:\\Users\\<USER>\\Downloads\\back1.png"
count = split_and_upload(file_path, upload_token)
print(count)
count1 = str(count)
complete_upload(upload_token,count1)
get_link(upload_token)

def submit_video_creating(prompt, cookie, url):
    url = "https://api-app-global.klingai.com/api/task/submit?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlzbSoJ4oP1qJUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR87uyWFzHoNWcUWNcTiC8KvwzS6RWycXZhFn6tQ1i48QMwoXBL9qinQV6ZopOnzfHReHR73UY4wsnqSzL-K_LBD6VO_tENJbOjbfQDxQZeqKf-V0P1F6KSOhtPeoSvJjvGNuq6oLJF7N--qFxX4qrfsRPsxUYGcl31Csc-HtZLTgtgpPduy7lQ0dHT1t0a_A8hnlVOU.$HE_b9a63e5b5926bb626165f359606806768af3f2f2f2f3dc6a5e48e82f8542b3ac20a965f369a4cc2889a4cc1af2&caver=2"

    payload = json.dumps({
    "type": "m2v_img2video",
    "arguments": [
        {
        "name": "prompt",
        "value": f"{prompt}"
        },
        {
        "name": "negative_prompt",
        "value": ""
        },
        {
        "name": "duration",
        "value": "5"
        },
        {
        "name": "imageCount",
        "value": "1"
        },
        {
        "name": "kling_version",
        "value": "1.6"
        },
        {
        "name": "cfg",
        "value": "0.5"
        },
        {
        "name": "camera_json",
        "value": "{\"type\":\"empty\",\"horizontal\":0,\"vertical\":0,\"zoom\":0,\"tilt\":0,\"pan\":0,\"roll\":0}"
        },
        {
        "name": "camera_control_enabled",
        "value": "false"
        },
        {
        "name": "biz",
        "value": "klingai"
        }
    ],
    "inputs": [
        {
        "inputType": "URL",
        "url": f"{url}",
        "name": "input"
        }
    ]
    })
    headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en',
    'content-type': 'application/json',
    'origin': 'https://app.klingai.com',
    'priority': 'u=1, i',
    'referer': 'https://app.klingai.com/',
    'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'time-zone': 'Asia/Saigon',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
    'Cookie': f"{cookie}"
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)
