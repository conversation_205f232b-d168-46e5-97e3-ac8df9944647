#!/usr/bin/env python3
"""
Test script để kiểm tra registration response và cookies
"""

import requests
from urllib.parse import quote
import random
import string
import json
import time

def test_full_registration():
    """Test full registration process với cookies"""
    print("🧪 Testing Full Registration Process...")
    
    # Tạo session
    session = requests.Session()
    
    # Setup headers
    session.headers.update({
        'accept': '*/*',
        'accept-language': 'vi,en-US;q=0.9,en;q=0.8',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    })
    
    # Tạo initial cookies
    session.cookies.set('weblogger_did', f'web_{random.randint(100000000000, 999999999999):X}', domain='.klingai.com')
    session.cookies.set('__risk_web_device_id', f'{random.randint(100000000000000, 999999999999999)}', domain='.klingai.com')
    session.cookies.set('KLING_LAST_ACCESS_REGION', 'global', domain='.klingai.com')
    session.cookies.set('_did', f'web_{random.randint(10000000000000, 99999999999999):X}', domain='.klingai.com')
    session.cookies.set('did', f'web_{random.randint(100000000000000, 999999999999999)}', domain='.klingai.com')
    
    # Thêm analytics cookies
    current_time = int(time.time())
    session.cookies.set('_gcl_au', f'1.1.{random.randint(100000000, 999999999)}.{current_time}', domain='.klingai.com')
    session.cookies.set('_ga', f'GA1.1.{random.randint(1000000000, 9999999999)}.{current_time}', domain='.klingai.com')
    session.cookies.set('_clck', f'{random.randint(1000000, 9999999)}%7C2%7Cfwy%7C0%7C{random.randint(1000, 9999)}', domain='.klingai.com')
    
    print(f"🍪 Initial cookies: {len(session.cookies)} cookies")
    
    # Test email và password
    email = f"testfull{random.randint(1000, 9999)}@simpace.edu.vn"
    password = "TestPassword123!"
    
    # Bước 1: Gửi code request
    print(f"\n📨 Step 1: Sending code request for {email}...")
    code_url = "https://id.klingai.com/pass/ksi18n/web/email/code?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR85WymFzHoNZcUWNcTiC8KvwzS6UWycXZhGA6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UjA8hnlVOU.$HE_f2ed751012bfb45f292eb827258364dd73b8b9b9b9b870211503a967cd0a55321ae12eb822ef8763c2ef8751b9&caver=2"
    
    encoded_email = quote(email, safe='')
    payload = f'sid=ksi18n.ai.portal&email={encoded_email}&type=439&language=en&isWebSig4=true'
    
    headers = {
        'content-type': 'application/x-www-form-urlencoded',
        'fe-id': '4d0021ac1750482685583251',
        'fe-kpf': 'PC',
        'fe-kpn': 'KLING',
        'origin': 'https://app.klingai.com',
        'referer': 'https://app.klingai.com/',
    }
    
    try:
        response = session.post(code_url, headers=headers, data=payload, timeout=10)
        print(f"✅ Code request status: {response.status_code}")
        print(f"🍪 Cookies after code request: {len(session.cookies)} cookies")
        
        if response.status_code == 200:
            # Bước 2: Giả lập có code (vì không thể lấy thực tế)
            fake_code = "123456"  # Fake code để test registration call
            print(f"\n📝 Step 2: Testing registration call with fake code...")
            
            # Registration request
            reg_url = "https://id.klingai.com/pass/ksi18n/web/register/emailPassword?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR845y2FzHoNfcUWNcTiC8KvwzS6UWycXZhGB6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UnA8hnlVOU.$HE_c7d84025278a816a1c1b8df76e0a21ae988d8c8c8c8d441420369905aa3e21dc2fd41b8d17dab256f7dab2648c&caver=2"
            
            encoded_password = quote(password, safe='')
            reg_payload = f'sid=ksi18n.ai.portal&email={encoded_email}&emailCode={fake_code}&password={encoded_password}&setCookie=true&language=en&isWebSig4=true'
            
            reg_response = session.post(reg_url, headers=headers, data=reg_payload, timeout=10)
            print(f"📝 Registration status: {reg_response.status_code}")
            print(f"📝 Registration response: {reg_response.text[:200]}...")
            
            # Parse response để xem structure
            try:
                reg_data = json.loads(reg_response.text)
                print(f"\n🔍 Registration response keys: {list(reg_data.keys())}")
                
                # Check for important fields
                important_fields = ['userId', 'passToken', 'ksi18n.ai.portal_st', 'ksi18n.ai.portal_at']
                for field in important_fields:
                    if field in reg_data:
                        value = reg_data[field]
                        print(f"🔑 {field}: {value[:50]}..." if len(str(value)) > 50 else f"🔑 {field}: {value}")
                    else:
                        print(f"❌ Missing {field}")
                        
            except json.JSONDecodeError:
                print("❌ Could not parse registration response as JSON")
        
        # Final cookies summary
        print(f"\n🍪 Final cookies summary:")
        print(f"Total cookies: {len(session.cookies)}")
        
        critical_cookies = ['userId', 'passToken', 'ksi18n.ai.portal_st', '__risk_web_device_id', 'ak_bmsc']
        for cookie_name in critical_cookies:
            found = any(cookie.name == cookie_name for cookie in session.cookies)
            print(f"  - {cookie_name}: {'✅' if found else '❌'}")
        
        # Generate cookie string
        cookie_list = []
        for cookie in session.cookies:
            cookie_list.append(f"{cookie.name}={cookie.value}")
        
        cookie_string = '; '.join(cookie_list)
        print(f"\n🍪 Final cookie string length: {len(cookie_string)} chars")
        print(f"🍪 Cookie string preview: {cookie_string[:300]}...")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_full_registration()
