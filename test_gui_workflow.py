#!/usr/bin/env python3
"""
Test script để kiểm tra workflow mới trong GUI
"""

import sys
import time
from kling_register_gui import EmailWorker
from PyQt5.QtCore import QCoreApplication

def test_workflow():
    """Test workflow mới"""
    print("🧪 Testing GUI Workflow...")
    
    # Tạo QCoreApplication để chạy QThread
    app = QCoreApplication(sys.argv)
    
    # Tạo worker với password test
    worker = EmailWorker("TestPassword123!", 1)
    
    # Connect signals để log
    worker.log_signal.connect(lambda msg: print(f"[LOG] {msg}"))
    worker.email_created_signal.connect(lambda email, name: print(f"[EMAIL] {email} ({name})"))
    worker.registration_complete_signal.connect(lambda success, info: print(f"[RESULT] Success: {success}, Info: {info}"))
    
    # Chạy worker
    worker.start()
    
    # Đợi worker hoàn thành
    worker.wait()
    
    print("✅ Test completed!")

if __name__ == "__main__":
    test_workflow()
