import requests

mail_name = input("Input mail name here:")
url = f"https://hunght1890.com/{mail_name}%40simpace.edu.vn"

payload = {}
headers = {
  'accept': '*/*',
  'accept-language': 'vi',
  'priority': 'u=1, i',
  'referer': 'https://hunght1890.com/',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)
