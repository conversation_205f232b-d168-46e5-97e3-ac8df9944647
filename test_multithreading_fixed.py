#!/usr/bin/env python3
"""
Test script để kiểm tra tính năng đa luồng đã được sửa
"""

import sys
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QObject, pyqtSignal
from kling_register_gui import MultiThreadWorker

class TestLogger(QObject):
    """Logger để test MultiThreadWorker"""
    
    def __init__(self):
        super().__init__()
        self.logs = []
        self.progress = 0
        self.emails = []
        self.results = []
    
    def log_message(self, message):
        """Log message"""
        timestamp = time.strftime("[%H:%M:%S]")
        log_entry = f"{timestamp} {message}"
        self.logs.append(log_entry)
        print(log_entry)
    
    def update_progress(self, value):
        """Update progress"""
        self.progress = value
        print(f"📈 Progress: {value}%")
    
    def email_created(self, email, mail_name):
        """<PERSON><PERSON> created"""
        self.emails.append((email, mail_name))
        print(f"📧 Email created: {email} (Name: {mail_name})")
    
    def registration_complete(self, success, result):
        """Registration complete"""
        self.results.append((success, result))
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{status}: {result[:100]}...")
    
    def worker_finished(self):
        """Worker finished"""
        print("🎉 Worker finished!")
        print(f"📊 Summary:")
        print(f"   Total logs: {len(self.logs)}")
        print(f"   Final progress: {self.progress}%")
        print(f"   Emails created: {len(self.emails)}")
        print(f"   Results: {len(self.results)}")
        
        successful = sum(1 for success, _ in self.results if success)
        failed = len(self.results) - successful
        print(f"   Successful: {successful}")
        print(f"   Failed: {failed}")

def test_multithreading():
    """Test tính năng đa luồng"""
    print("🧪 Testing MultiThreadWorker...")
    
    # Tạo QApplication (cần thiết cho QThread)
    app = QApplication(sys.argv)
    
    # Tạo logger
    logger = TestLogger()
    
    # Tạo worker với 2 accounts, 2 threads
    password = "TestPassword123!"
    count = 2
    threads = 2
    
    print(f"🚀 Starting test with {count} accounts, {threads} threads")
    
    worker = MultiThreadWorker(password, count, threads)
    
    # Connect signals
    worker.log_signal.connect(logger.log_message)
    worker.progress_signal.connect(logger.update_progress)
    worker.email_created_signal.connect(logger.email_created)
    worker.registration_complete_signal.connect(logger.registration_complete)
    worker.finished.connect(logger.worker_finished)
    
    # Start worker
    worker.start()
    
    # Đợi worker hoàn thành (timeout 120 giây)
    if worker.wait(120000):  # 120 seconds
        print("✅ Worker completed successfully")
    else:
        print("❌ Worker timeout")
        worker.stop()
        worker.wait(5000)  # Wait 5 more seconds
    
    # Cleanup
    app.quit()
    
    return logger

if __name__ == "__main__":
    logger = test_multithreading()
    
    print("\n" + "="*50)
    print("🎯 TEST RESULTS:")
    print("="*50)
    
    if logger.results:
        successful = sum(1 for success, _ in logger.results if success)
        total = len(logger.results)
        success_rate = (successful / total) * 100 if total > 0 else 0
        
        print(f"📊 Success Rate: {success_rate:.1f}% ({successful}/{total})")
        print(f"📧 Emails Created: {len(logger.emails)}")
        print(f"📝 Log Entries: {len(logger.logs)}")
        print(f"📈 Final Progress: {logger.progress}%")
        
        if logger.emails:
            print(f"\n📧 Sample Emails:")
            for i, (email, mail_name) in enumerate(logger.emails[:3], 1):
                print(f"   {i}. {email} (Name: {mail_name})")
        
        if logger.results:
            print(f"\n📋 Sample Results:")
            for i, (success, result) in enumerate(logger.results[:3], 1):
                status = "✅" if success else "❌"
                print(f"   {i}. {status} {result[:80]}...")
    else:
        print("❌ No results generated")
    
    print("\n🎉 Test completed!")
