#!/usr/bin/env python3
"""
Test script để tạo account thật và kiểm tra cookies
"""

import requests
from urllib.parse import quote
import random
import string
import json
import time
import re

def generate_random_email():
    """Tạo random email"""
    username_length = random.randint(10, 14)
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=username_length))
    email = f"{username}@simpace.edu.vn"
    return email, username

def get_verification_code_from_tempmail(mail_name, max_retries=5, wait_time=5):
    """L<PERSON>y mã xác thực từ tempmail"""
    url = f"https://hunght1890.com/{mail_name}%40simpace.edu.vn"
    
    headers = {
        'accept': '*/*',
        'accept-language': 'vi',
        'priority': 'u=1, i',
        'referer': 'https://hunght1890.com/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
    }
    
    print(f"🔍 Đang tìm mã xác thực cho {mail_name}@simpace.edu.vn...")
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                try:
                    emails = response.json()
                    print(f"📧 Lần thử {attempt + 1}: Tìm thấy {len(emails)} email")
                    
                    for email in emails:
                        subject = email.get('subject', '')
                        body = email.get('body', '')
                        
                        # Kiểm tra email từ KlingAI
                        if 'klingai' in subject.lower() or 'kling' in subject.lower():
                            print(f"✅ Tìm thấy email từ KlingAI: {subject}")
                            
                            # Extract mã xác thực 6 số
                            code_match = re.search(r'\b\d{6}\b', body)
                            if code_match:
                                code = code_match.group()
                                print(f"🔑 Mã xác thực: {code}")
                                return code
                            else:
                                print("❌ Không tìm thấy mã 6 số trong email")
                    
                    if len(emails) == 0:
                        print(f"⏳ Chưa có email nào. Đợi {wait_time}s...")
                        time.sleep(wait_time)
                    else:
                        print("❌ Không tìm thấy email từ KlingAI")
                        
                except json.JSONDecodeError:
                    print(f"❌ Lỗi parse JSON: {response.text[:100]}...")
            else:
                print(f"❌ HTTP Error {response.status_code}")
                
        except Exception as e:
            print(f"❌ Lỗi kết nối: {str(e)}")
            
        if attempt < max_retries - 1:
            print(f"⏳ Thử lại sau {wait_time}s...")
            time.sleep(wait_time)
    
    print("❌ Không thể lấy mã xác thực sau nhiều lần thử")
    return None

def test_real_account_creation():
    """Test tạo account thật với real verification code"""
    print("🧪 Testing Real Account Creation...")
    
    # Tạo session
    session = requests.Session()
    
    # Setup headers
    session.headers.update({
        'accept': '*/*',
        'accept-language': 'vi,en-US;q=0.9,en;q=0.8',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
    })
    
    # Tạo initial cookies
    session.cookies.set('weblogger_did', f'web_{random.randint(************, ************):X}', domain='.klingai.com')
    session.cookies.set('__risk_web_device_id', f'{random.randint(************000, ***************)}', domain='.klingai.com')
    session.cookies.set('KLING_LAST_ACCESS_REGION', 'global', domain='.klingai.com')
    session.cookies.set('_did', f'web_{random.randint(************00, ************99):X}', domain='.klingai.com')
    session.cookies.set('did', f'web_{random.randint(************000, ***************)}', domain='.klingai.com')
    
    # Thêm analytics cookies
    current_time = int(time.time())
    session.cookies.set('_gcl_au', f'1.1.{random.randint(100000000, 999999999)}.{current_time}', domain='.klingai.com')
    session.cookies.set('_ga', f'GA1.1.{random.randint(1000000000, 9999999999)}.{current_time}', domain='.klingai.com')
    session.cookies.set('_clck', f'{random.randint(1000000, 9999999)}%7C2%7Cfwy%7C0%7C{random.randint(1000, 9999)}', domain='.klingai.com')
    
    print(f"🍪 Initial cookies: {len(session.cookies)} cookies")
    
    # Bước 1: Tạo random email
    email, mail_name = generate_random_email()
    password = "TestCookies123!"
    print(f"📧 Email: {email}")
    print(f"📋 Mail name: {mail_name}")
    
    # Bước 2: Gửi code request
    print(f"\n📨 Gửi yêu cầu mã xác thực...")
    code_url = "https://id.klingai.com/pass/ksi18n/web/email/code?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR85WymFzHoNZcUWNcTiC8KvwzS6UWycXZhGA6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UjA8hnlVOU.$HE_f2ed751012bfb45f292eb827258364dd73b8b9b9b9b870211503a967cd0a55321ae12eb822ef8763c2ef8751b9&caver=2"
    
    encoded_email = quote(email, safe='')
    payload = f'sid=ksi18n.ai.portal&email={encoded_email}&type=439&language=en&isWebSig4=true'
    
    headers = {
        'content-type': 'application/x-www-form-urlencoded',
        'fe-id': '4d0021ac1750482685583251',
        'fe-kpf': 'PC',
        'fe-kpn': 'KLING',
        'origin': 'https://app.klingai.com',
        'referer': 'https://app.klingai.com/',
    }
    
    try:
        response = session.post(code_url, headers=headers, data=payload, timeout=10)
        print(f"✅ Code request status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"🍪 Cookies after code request: {len(session.cookies)} cookies")
            
            # Bước 3: Đợi và lấy verification code
            print(f"\n⏳ Đợi 10 giây để email được gửi...")
            time.sleep(10)
            
            verification_code = get_verification_code_from_tempmail(mail_name)
            
            if verification_code:
                print(f"✅ Lấy được mã xác thực: {verification_code}")
                
                # Bước 4: Đăng ký với real code
                print(f"\n📝 Đăng ký tài khoản với mã thật...")
                reg_url = "https://id.klingai.com/pass/ksi18n/web/register/emailPassword?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR845y2FzHoNfcUWNcTiC8KvwzS6UWycXZhGB6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UnA8hnlVOU.$HE_c7d84025278a816a1c1b8df76e0a21ae988d8c8c8c8d441420369905aa3e21dc2fd41b8d17dab256f7dab2648c&caver=2"
                
                encoded_password = quote(password, safe='')
                reg_payload = f'sid=ksi18n.ai.portal&email={encoded_email}&emailCode={verification_code}&password={encoded_password}&setCookie=true&language=en&isWebSig4=true'
                
                reg_response = session.post(reg_url, headers=headers, data=reg_payload, timeout=10)
                print(f"📝 Registration status: {reg_response.status_code}")
                print(f"📝 Registration response: {reg_response.text}")
                
                # Parse response
                try:
                    reg_data = json.loads(reg_response.text)
                    print(f"\n🔍 Registration response keys: {list(reg_data.keys())}")
                    
                    if reg_data.get('result') == 1:
                        print("🎉 Registration successful!")
                        
                        # Extract important data
                        user_id = reg_data.get('userId')
                        if user_id:
                            print(f"👤 User ID: {user_id}")
                            session.cookies.set('userId', str(user_id), domain='.klingai.com')
                        
                        # Check for tokens in response
                        important_fields = ['passToken', 'ksi18n.ai.portal_st', 'ksi18n.ai.portal_at', 'ksi18n.ai.portal_ph']
                        for field in important_fields:
                            if field in reg_data:
                                value = reg_data[field]
                                session.cookies.set(field, value, domain='.klingai.com')
                                print(f"🔑 Added {field}: {value[:50]}...")
                        
                        # Final cookies analysis
                        print(f"\n🍪 Final cookies analysis:")
                        print(f"Total cookies: {len(session.cookies)}")
                        
                        critical_cookies = ['userId', 'passToken', 'ksi18n.ai.portal_st', '__risk_web_device_id', 'ak_bmsc']
                        for cookie_name in critical_cookies:
                            found = any(cookie.name == cookie_name for cookie in session.cookies)
                            print(f"  - {cookie_name}: {'✅' if found else '❌'}")
                        
                        # Generate final cookie string
                        cookie_list = []
                        for cookie in session.cookies:
                            cookie_list.append(f"{cookie.name}={cookie.value}")
                        
                        cookie_string = '; '.join(cookie_list)
                        print(f"\n🍪 Final cookie string:")
                        print(f"Length: {len(cookie_string)} chars")
                        print(f"Count: {len(cookie_list)} cookies")
                        print(f"Preview: {cookie_string[:500]}...")
                        
                        # Save to file for testing
                        with open("test_account_cookies.txt", "w") as f:
                            f.write(f"# Test Account Created Successfully\n")
                            f.write(f"# Email: {email}\n")
                            f.write(f"# Password: {password}\n")
                            f.write(f"# User ID: {user_id}\n")
                            f.write(f"# Cookie String:\n")
                            f.write(f"{user_id}|{email}|{password}|{cookie_string}\n")
                        
                        print(f"\n💾 Account saved to test_account_cookies.txt")
                        
                    else:
                        print(f"❌ Registration failed: {reg_data}")
                        
                except json.JSONDecodeError:
                    print("❌ Could not parse registration response as JSON")
            else:
                print("❌ Không thể lấy mã xác thực")
        else:
            print(f"❌ Code request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_real_account_creation()
