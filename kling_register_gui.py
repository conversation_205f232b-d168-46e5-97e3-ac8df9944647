import sys
import time
import threading
import re
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QLineEdit, QTextEdit, 
                             QProgressBar, QGroupBox, QGridLayout, QSpinBox,
                             QMessageBox, QTabWidget, QTableWidget, QTableWidgetItem,
                             QHeaderView, QCheckBox)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont, QIcon, QPixmap
import requests
from urllib.parse import quote
import random
import string
import json

class EmailWorker(QThread):
    """Worker thread để tạo email và xử lý đăng ký"""
    log_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)
    email_created_signal = pyqtSignal(str, int)  # email, mail_id
    registration_complete_signal = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, password, count=1, token=None):
        super().__init__()
        self.password = password
        self.count = count

        # Sử dụng token từ input hoặc default
        if token:
            self.tokens = [token]
        else:
            self.tokens = [
                "4890|AEunrjmEr5f1B5i6UwNOlpWfKaTz8bBcJf34vu2m73c5ca98",
                # Thêm tokens backup ở đây nếu có
            ]

        self.current_token_index = 0
        self.token = self.tokens[self.current_token_index]
        self.domain = "tempmail.ckvn.edu.vn"
        self.running = True

    def stop(self):
        self.running = False

    def switch_to_next_token(self):
        """Chuyển sang token tiếp theo khi token hiện tại hết quota"""
        if self.current_token_index < len(self.tokens) - 1:
            self.current_token_index += 1
            self.token = self.tokens[self.current_token_index]
            self.log_signal.emit(f"🔄 Chuyển sang token backup #{self.current_token_index + 1}")
            return True
        else:
            self.log_signal.emit("❌ Đã hết tất cả tokens backup!")
            return False
        
    def create_temp_email(self):
        """Tạo email temp với retry logic"""
        url = "https://tempmail.id.vn/api/email/create"
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.token}'
        }

        # Retry logic cho rate limiting
        max_retries = 3
        for attempt in range(max_retries):
            username_length = random.randint(10, 14)
            username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=username_length))

            payload = {
                "user": username,
                "domain": self.domain
            }

            try:
                response = requests.post(url, headers=headers, json=payload, timeout=10)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        email = result.get('data', {}).get('email')
                        mail_id = result.get('data', {}).get('id')
                        return email, mail_id
                    else:
                        self.log_signal.emit(f"❌ Lỗi tạo email: {result.get('message', 'Unknown error')}")
                        return None, None

                elif response.status_code == 400:
                    # Check nếu là lỗi giới hạn email
                    try:
                        error_data = response.json()
                        error_message = error_data.get('message', '')

                        if 'giới hạn email' in error_message or 'limit' in error_message.lower():
                            self.log_signal.emit(f"⚠️ Token hết quota: {error_message}")
                            # Thử chuyển sang token khác
                            if self.switch_to_next_token():
                                self.log_signal.emit("🔄 Thử lại với token mới...")
                                continue
                            else:
                                self.log_signal.emit("❌ Không còn token backup nào!")
                                return None, None
                        else:
                            self.log_signal.emit(f"❌ API error 400: {error_message}")
                            return None, None
                    except:
                        self.log_signal.emit(f"❌ API error 400: {response.text}")
                        return None, None

                elif response.status_code == 429:
                    # Rate limited - đợi và thử lại
                    wait_time = (attempt + 1) * 15  # 15, 30, 45 giây
                    self.log_signal.emit(f"⚠️ Rate limited, đợi {wait_time} giây...")
                    time.sleep(wait_time)
                    continue

                else:
                    self.log_signal.emit(f"❌ API error: {response.status_code} - {response.text}")
                    if attempt < max_retries - 1:
                        self.log_signal.emit(f"🔄 Thử lại lần {attempt + 2}...")
                        time.sleep(10)
                        continue

            except Exception as e:
                self.log_signal.emit(f"❌ Lỗi kết nối API: {str(e)}")
                if attempt < max_retries - 1:
                    self.log_signal.emit(f"🔄 Thử lại lần {attempt + 2}...")
                    time.sleep(10)
                    continue

        return None, None

    def create_temp_email_alternative(self):
        """Tạo email temp bằng service khác khi tempmail.id.vn hết quota"""
        try:
            # Sử dụng 1secmail.com API (free, không cần token)
            self.log_signal.emit("🔄 Thử service backup: 1secmail.com...")

            # Get available domains
            domains_url = "https://www.1secmail.com/api/v1/?action=getDomainList"
            domains_response = requests.get(domains_url, timeout=10)

            if domains_response.status_code == 200:
                domains = domains_response.json()
                if domains:
                    domain = domains[0]  # Lấy domain đầu tiên

                    # Generate random username
                    username_length = random.randint(10, 14)
                    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=username_length))

                    email = f"{username}@{domain}"

                    # 1secmail không cần tạo email trước, chỉ cần format đúng
                    self.log_signal.emit(f"✅ Tạo email backup thành công: {email}")
                    return email, f"{username}@{domain}"  # Dùng email làm mail_id

            self.log_signal.emit("❌ Không thể tạo email backup")
            return None, None

        except Exception as e:
            self.log_signal.emit(f"❌ Lỗi tạo email backup: {str(e)}")
            return None, None
    
    def get_code(self, email):
        """Gửi yêu cầu mã xác thực"""
        url = "https://id.klingai.com/pass/ksi18n/web/email/code?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR85WymFzHoNZcUWNcTiC8KvwzS6UWycXZhGA6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UjA8hnlVOU.$HE_f2ed751012bfb45f292eb827258364dd73b8b9b9b9b870211503a967cd0a55321ae12eb822ef8763c2ef8751b9&caver=2"
        
        encoded_email = quote(email, safe='')
        payload = f'sid=ksi18n.ai.portal&email={encoded_email}&type=439&language=en&isWebSig4=true'
        headers = {
            'accept': '*/*',
            'accept-language': 'vi',
            'content-type': 'application/x-www-form-urlencoded',
            'fe-id': '4d0021ac1750482685583251',
            'fe-kpf': 'PC',
            'fe-kpn': 'KLING',
            'origin': 'https://app.klingai.com',
            'priority': 'u=1, i',
            'referer': 'https://app.klingai.com/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }
        
        try:
            response = requests.post(url, headers=headers, data=payload, timeout=10)
            return response.status_code == 200
        except Exception as e:
            self.log_signal.emit(f"❌ Lỗi gửi mã: {str(e)}")
            return False
    
    def get_messages(self, mail_id):
        """Lấy tin nhắn từ email"""
        url = f"https://tempmail.id.vn/api/email/{str(mail_id)}"
        headers = {
            'Accept': 'application/json',
            'Authorization': f'Bearer {self.token}'
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result.get('success'):
                return result.get('data', [])
            return []
        except Exception:
            return []
    
    def extract_code_from_message(self, message_id):
        """Trích xuất mã từ tin nhắn"""
        url = f"https://tempmail.id.vn/api/message/{str(message_id)}"
        headers = {
            'Authorization': f'Bearer {self.token}'
        }

        try:
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                # Parse JSON response để lấy nội dung HTML
                try:
                    data = response.json()
                    if data.get('success') and 'data' in data:
                        # Lấy nội dung HTML từ field 'content' hoặc 'body'
                        html_content = data['data'].get('content', '') or data['data'].get('body', '')
                        self.log_signal.emit(f"📄 Nội dung HTML email: {html_content[:300]}...")

                        # Tìm mã 6 chữ số trong HTML content
                        patterns = [
                            r'>(\d{6})<',  # Trong HTML tag (ưu tiên cao nhất)
                            r'code[:\s]*(\d{6})',  # "code: 123456"
                            r'verification[:\s]*(\d{6})',  # "verification: 123456"
                            r'\b(\d{6})\b',  # 6 chữ số đơn giản (cuối cùng)
                        ]

                        for pattern in patterns:
                            code_matches = re.findall(pattern, html_content, re.IGNORECASE)
                            if code_matches:
                                # Lấy mã cuối cùng tìm được (thường là mã chính xác nhất)
                                code = code_matches[-1]
                                self.log_signal.emit(f"🎯 Tìm thấy mã với pattern '{pattern}': {code}")
                                return code

                        self.log_signal.emit(f"❌ Không tìm thấy mã 6 chữ số trong HTML content")
                    else:
                        self.log_signal.emit(f"❌ Response không có data: {data}")
                except json.JSONDecodeError:
                    # Fallback: nếu không parse được JSON, dùng text content
                    content = response.text
                    self.log_signal.emit(f"📄 Fallback - Nội dung text: {content[:200]}...")

                    patterns = [
                        r'>(\d{6})<',  # Trong HTML tag
                        r'\b(\d{6})\b',  # 6 chữ số đơn giản
                    ]

                    for pattern in patterns:
                        code_matches = re.findall(pattern, content, re.IGNORECASE)
                        if code_matches:
                            code = code_matches[-1]
                            self.log_signal.emit(f"🎯 Fallback - Tìm thấy mã: {code}")
                            return code
            else:
                self.log_signal.emit(f"❌ Lỗi lấy nội dung tin nhắn: {response.status_code}")
            return None
        except Exception as e:
            self.log_signal.emit(f"❌ Lỗi trích xuất mã: {str(e)}")
            return None
    
    def generate_cookie_string(self, email):
        """Tạo cookie string theo format chuẩn"""
        import random
        import time
        import hashlib

        # Tạo các giá trị random cho cookies
        current_time = int(time.time())

        # Tạo device ID từ email
        device_hash = hashlib.md5(email.encode()).hexdigest()
        device_id = f"web_{device_hash[:32]}"

        # Tạo risk device ID
        risk_device_id = f"e{random.randint(100000000000000, 999999999999999)}"

        # Tạo weblogger did
        weblogger_did = f"web_{random.randint(100000000000, 999999999999):X}"

        # Tạo _did
        _did = f"web_{random.randint(10000000000000, 99999999999999):X}"

        # Tạo gcl values
        gcl_gs = f"2.1.k1$i{current_time}$u{random.randint(10000000, 99999999)}"
        gcl_au = f"1.1.{random.randint(100000000, 999999999)}.{current_time}"
        gcl_aw = f"GCL.{current_time}.EAIaIQobChMI{random.randint(1000000000, 9999999999)}"

        # Tạo GA values
        ga_value = f"GA1.1.{random.randint(1000000000, 9999999999)}.{current_time}"

        # Tạo clck value
        clck_value = f"{random.randint(1000000, 9999999)}%7C2%7Cfwy%7C0%7C{random.randint(1000, 9999)}"

        # Tạo userId (sẽ được cập nhật sau khi đăng ký thành công)
        user_id = str(random.randint(30000000, 99999999))

        # Tạo portal tokens (giả lập)
        portal_st = f"ChNrc2kxOG4uYWkucG9ydGFsLnN0EqABA{random.randint(100000, 999999)}"
        portal_ph = f"e{random.randint(1000000000000000, 9999999999999999)}"

        # Tạo ak_bmsc (phức tạp) - Độ dài thực tế ~500 chars
        ak_bmsc_part1 = ''.join(random.choices('ABCDEF0123456789', k=32))
        ak_bmsc_part2 = '000000000000000000000000000000'
        # Part 3 phải dài hơn nhiều để match với thực tế
        ak_bmsc_part3_base = f"YAAQ{random.choice(['HBBFd', 'kmjJF', 'GhBFd', 'jbUuF'])}"
        ak_bmsc_part3_random = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=', k=400))
        ak_bmsc_part3 = f"{ak_bmsc_part3_base}{ak_bmsc_part3_random}"
        ak_bmsc = f"{ak_bmsc_part1}~{ak_bmsc_part2}~{ak_bmsc_part3}"

        # Tạo clsk
        clsk_value = f"{random.randint(1000000, 9999999)}%7C{current_time * 1000}%7C2%7C1%7Cz.clarity.ms%2Fcollect"

        # Tạo GA MWG30LDQKZ
        ga_mwg = f"GS2.1.s{current_time}$o{random.randint(1, 9)}$g1$t{current_time + random.randint(100, 1000)}$j58$l0$h{random.randint(100000000, 999999999)}"

        # Tạo uetsid và uetvid
        uetsid = f"{random.randint(1000000000000000, 9999999999999999)}"
        uetvid = f"{random.randint(1000000000000000, 9999999999999999)}"

        # Tạo bm_sv (phức tạp) - Độ dài thực tế ~200+ chars
        bm_sv_part1 = ''.join(random.choices('ABCDEF0123456789', k=32))
        bm_sv_part2_base = f"YAAQ{random.choice(['kmjJF', 'GhBFd', 'HBBFd', 'jbUuF'])}"
        bm_sv_part2_random = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=', k=150))
        bm_sv_part2 = f"{bm_sv_part2_base}{bm_sv_part2_random}"
        bm_sv = f"{bm_sv_part1}~{bm_sv_part2}~1"

        # Tạo cookie string theo thứ tự chuẩn
        cookies = [
            f"weblogger_did={weblogger_did}",
            f"__risk_web_device_id={risk_device_id}",
            f"KLING_LAST_ACCESS_REGION=global",
            f"_did={_did}",
            f"did={device_id}",
            f"_gcl_gs={gcl_gs}",
            f"_gcl_au={gcl_au}",
            f"_gcl_aw={gcl_aw}",
            f"_ga={ga_value}",
            f"_clck={clck_value}",
            f"userId={user_id}",
            f"ksi18n.ai.portal_st={portal_st}",
            f"ksi18n.ai.portal_ph={portal_ph}",
            f"ak_bmsc={ak_bmsc}",
            f"_clsk={clsk_value}",
            f"_ga_MWG30LDQKZ={ga_mwg}",
            f"_uetsid={uetsid}",
            f"_uetvid={uetvid}",
            f"bm_sv={bm_sv}"
        ]

        return '; '.join(cookies)

    def extract_real_cookies_from_browser(self):
        """
        Hướng dẫn extract cookies thực từ browser
        Trả về hướng dẫn thay vì generate fake cookies
        """
        instructions = """
        🍪 HƯỚNG DẪN LẤY COOKIE THỰC TỪ BROWSER:

        1. Mở Chrome/Edge
        2. Đăng nhập https://app.klingai.com
        3. Nhấn F12 (Developer Tools)
        4. Vào tab Application/Storage
        5. Chọn Cookies > https://app.klingai.com
        6. Copy tất cả cookies theo format:
           name1=value1; name2=value2; name3=value3

        ⚠️ QUAN TRỌNG:
        - ak_bmsc phải có độ dài ~500 chars
        - bm_sv phải có độ dài ~200 chars
        - Phải có đầy đủ: userId, ksi18n.ai.portal_st, _did, did

        💡 TIP: Sử dụng cookie thực sẽ hoạt động tốt hơn fake cookies!
        """
        return instructions

    def register(self, email, password, code):
        """Đăng ký tài khoản"""
        url = "https://id.klingai.com/pass/ksi18n/web/register/emailPassword?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR845y2FzHoNfcUWNcTiC8KvwzS6UWycXZhGB6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UnA8hnlVOU.$HE_c7d84025278a816a1c1b8df76e0a21ae988d8c8c8c8d441420369905aa3e21dc2fd41b8d17dab256f7dab2648c&caver=2"

        encoded_email = quote(email, safe='')
        encoded_password = quote(password, safe='')
        payload = f'sid=ksi18n.ai.portal&email={encoded_email}&emailCode={code}&password={encoded_password}&setCookie=true&language=en&isWebSig4=true'

        headers = {
            'accept': '*/*',
            'accept-language': 'vi',
            'content-type': 'application/x-www-form-urlencoded',
            'fe-id': '4d0021ac1750482685583251',
            'fe-kpf': 'PC',
            'fe-kpn': 'KLING',
            'origin': 'https://app.klingai.com',
            'priority': 'u=1, i',
            'referer': 'https://app.klingai.com/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }

        try:
            response = requests.post(url, headers=headers, data=payload, timeout=10)
            return response.status_code == 200, response.text
        except Exception as e:
            return False, str(e)
    
    def run(self):
        """Chạy quy trình đăng ký"""
        for i in range(self.count):
            if not self.running:
                break
                
            self.log_signal.emit(f"\n🚀 Bắt đầu đăng ký tài khoản {i+1}/{self.count}")
            
            # Tạo email
            self.log_signal.emit("📧 Tạo email temp...")
            email, mail_id = self.create_temp_email()
            if not email:
                self.log_signal.emit("❌ Không thể tạo email temp")
                continue
                
            self.log_signal.emit(f"✅ Tạo email thành công: {email}")
            self.email_created_signal.emit(email, mail_id)
            
            # Gửi mã xác thực
            self.log_signal.emit("📨 Gửi yêu cầu mã xác thực...")
            if not self.get_code(email):
                self.log_signal.emit("❌ Không thể gửi mã xác thực")
                continue
                
            self.log_signal.emit("✅ Gửi yêu cầu thành công")
            
            # Đợi và lấy mã
            self.log_signal.emit("⏳ Đợi nhận mã xác thực...")
            code = None
            for attempt in range(20):  # Đợi tối đa 60 giây (20 x 3s)
                if not self.running:
                    break

                # Log tiến trình kiểm tra
                if attempt > 0 and attempt % 3 == 0:  # Log mỗi 9 giây
                    self.log_signal.emit(f"⏳ Đang kiểm tra email... ({attempt * 3}s/{20 * 3}s)")

                messages = self.get_messages(mail_id)
                if messages:
                    self.log_signal.emit(f"📬 Nhận được dữ liệu tin nhắn")
                    # Debug: Log cấu trúc dữ liệu
                    self.log_signal.emit(f"🔍 Kiểu dữ liệu messages: {type(messages)}")

                    # Nếu messages là list
                    if isinstance(messages, list):
                        self.log_signal.emit(f"📬 Tìm thấy {len(messages)} tin nhắn")
                        if messages:
                            self.log_signal.emit(f"🔍 Cấu trúc tin nhắn đầu tiên: {type(messages[0])}")
                            if isinstance(messages[0], dict):
                                self.log_signal.emit(f"🔍 Keys: {list(messages[0].keys())}")

                        for i, msg in enumerate(messages):
                            self.log_signal.emit(f"📧 Kiểm tra tin nhắn {i+1}: {type(msg)}")

                            # Kiểm tra nếu msg là dict
                            if isinstance(msg, dict):
                                sender = msg.get('from', '').lower()
                                subject = msg.get('subject', '').lower()
                                self.log_signal.emit(f"📧 From: {msg.get('from', 'N/A')}")
                                self.log_signal.emit(f"📧 Subject: {msg.get('subject', 'N/A')}")

                                # Kiểm tra email từ Kling AI (<EMAIL>)
                                if ('klingai' in sender or 'kwai.com' in sender or
                                    'kling' in subject or 'verification' in subject.lower()):
                                    self.log_signal.emit(f"✅ Tìm thấy email từ Kling AI!")
                                    code = self.extract_code_from_message(msg.get('id'))
                                    if code:
                                        self.log_signal.emit(f"🎯 Trích xuất được mã: {code}")
                                        break
                                    else:
                                        self.log_signal.emit(f"❌ Không tìm thấy mã trong tin nhắn")
                            # Nếu msg là string, có thể là message content trực tiếp
                            elif isinstance(msg, str):
                                self.log_signal.emit(f"📧 String message: {msg[:100]}...")
                                if 'kling' in msg.lower():
                                    # Tìm mã 6 chữ số trong string
                                    import re
                                    code_match = re.search(r'\b\d{6}\b', msg)
                                    if code_match:
                                        code = code_match.group()
                                        self.log_signal.emit(f"🎯 Tìm thấy mã trong string: {code}")
                                        break

                    # Nếu messages là dict (có thể chứa items)
                    elif isinstance(messages, dict):
                        self.log_signal.emit(f"🔍 Messages là dict với keys: {list(messages.keys())}")
                        # API trả về cấu trúc {'items': [...], 'pagination': {...}}
                        if 'items' in messages:
                            actual_messages = messages['items']
                            if isinstance(actual_messages, list):
                                self.log_signal.emit(f"📬 Tìm thấy {len(actual_messages)} tin nhắn trong items")
                                for i, msg in enumerate(actual_messages):
                                    self.log_signal.emit(f"📧 Kiểm tra tin nhắn {i+1}: {type(msg)}")

                                    if isinstance(msg, dict):
                                        sender = msg.get('from', '').lower()
                                        subject = msg.get('subject', '').lower()
                                        self.log_signal.emit(f"📧 From: {msg.get('from', 'N/A')}")
                                        self.log_signal.emit(f"📧 Subject: {msg.get('subject', 'N/A')}")

                                        # Kiểm tra email từ Kling AI (<EMAIL>)
                                        if ('klingai' in sender or 'kwai.com' in sender or
                                            'kling' in subject or 'verification' in subject.lower()):
                                            self.log_signal.emit(f"✅ Tìm thấy email từ Kling AI!")
                                            code = self.extract_code_from_message(msg.get('id'))
                                            if code:
                                                self.log_signal.emit(f"🎯 Trích xuất được mã: {code}")
                                                break
                                            else:
                                                self.log_signal.emit(f"❌ Không tìm thấy mã trong tin nhắn")
                            else:
                                self.log_signal.emit(f"❌ Items không phải là list: {type(actual_messages)}")
                        else:
                            self.log_signal.emit(f"❌ Không tìm thấy key 'items' trong response")
                    if code:
                        break

                time.sleep(3)  # Đợi 3 giây giữa mỗi lần kiểm tra để tránh rate limiting
                
            if not code:
                self.log_signal.emit("❌ Không nhận được mã xác thực")
                continue
                
            self.log_signal.emit(f"✅ Nhận được mã: {code}")
            
            # Đăng ký
            self.log_signal.emit("📝 Đăng ký tài khoản...")
            success, response = self.register(email, self.password, code)

            if success:
                self.log_signal.emit(f"✅ Đăng ký thành công: {email}")

                # Tạo cookie string mới theo format chuẩn
                cookie_string = self.generate_cookie_string(email)
                if cookie_string:
                    self.log_signal.emit(f"🍪 Generated cookie string with {len(cookie_string.split(';'))} cookies")



                # Tạo account info với cookie string
                account_info = {
                    'email': email,
                    'password': self.password,
                    'cookie_string': cookie_string
                }
                self.registration_complete_signal.emit(True, json.dumps(account_info))
            else:
                self.log_signal.emit(f"❌ Đăng ký thất bại: {response}")
                self.registration_complete_signal.emit(False, f"{email}:FAILED")
                
            self.progress_signal.emit(int((i+1)/self.count * 100))
            
            if i < self.count - 1:
                self.log_signal.emit("⏳ Đợi 15 giây trước khi tạo tài khoản tiếp theo...")
                time.sleep(15)

class KlingRegisterGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.worker = None
        self.successful_accounts = []
        self.failed_accounts = []
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("Kling AI Auto Register Tool")
        self.setGeometry(100, 100, 1000, 700)

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)

        # Title
        title_label = QLabel("🤖 Kling AI Auto Register Tool")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2E86AB; margin: 10px;")
        main_layout.addWidget(title_label)

        # Tab widget
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)

        # Tab 1: Register
        register_tab = QWidget()
        tab_widget.addTab(register_tab, "🚀 Đăng ký")

        register_layout = QVBoxLayout(register_tab)

        # Settings group
        settings_group = QGroupBox("⚙️ Cài đặt")
        settings_layout = QGridLayout(settings_group)

        # Password
        settings_layout.addWidget(QLabel("Mật khẩu:"), 0, 0)
        self.password_input = QLineEdit()
        self.password_input.setText("Anhtu3112@")
        self.password_input.setEchoMode(QLineEdit.Password)
        settings_layout.addWidget(self.password_input, 0, 1)

        # Show password checkbox
        self.show_password_cb = QCheckBox("Hiện mật khẩu")
        self.show_password_cb.toggled.connect(self.toggle_password_visibility)
        settings_layout.addWidget(self.show_password_cb, 0, 2)

        # Count
        settings_layout.addWidget(QLabel("Số lượng tài khoản:"), 1, 0)
        self.count_input = QSpinBox()
        self.count_input.setMinimum(1)
        self.count_input.setMaximum(100)
        self.count_input.setValue(1)
        settings_layout.addWidget(self.count_input, 1, 1)

        # Tempmail Token
        settings_layout.addWidget(QLabel("Token Tempmail:"), 2, 0)
        self.token_input = QLineEdit()
        self.token_input.setText("4890|AEunrjmEr5f1B5i6UwNOlpWfKaTz8bBcJf34vu2m73c5ca98")
        self.token_input.setPlaceholderText("Nhập token từ tempmail.id.vn...")
        settings_layout.addWidget(self.token_input, 2, 1)

        # Test token button
        self.test_token_btn = QPushButton("🧪 Test Token")
        self.test_token_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px 15px;
                font-size: 12px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        self.test_token_btn.clicked.connect(self.test_token)
        settings_layout.addWidget(self.test_token_btn, 2, 2)

        register_layout.addWidget(settings_group)

        # Control buttons
        control_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 Bắt đầu đăng ký")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.start_btn.clicked.connect(self.start_registration)
        control_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("⏹️ Dừng")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_registration)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)

        control_layout.addStretch()
        register_layout.addLayout(control_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #28a745;
                border-radius: 3px;
            }
        """)
        register_layout.addWidget(self.progress_bar)

        # Log area
        log_group = QGroupBox("📋 Nhật ký")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        log_layout.addWidget(self.log_text)

        # Clear log button
        clear_log_btn = QPushButton("🗑️ Xóa log")
        clear_log_btn.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_log_btn)

        register_layout.addWidget(log_group)

        # Tab 2: Results
        results_tab = QWidget()
        tab_widget.addTab(results_tab, "📊 Kết quả")

        results_layout = QVBoxLayout(results_tab)

        # Statistics
        stats_group = QGroupBox("📈 Thống kê")
        stats_layout = QGridLayout(stats_group)

        self.success_label = QLabel("Thành công: 0")
        self.success_label.setStyleSheet("color: #28a745; font-weight: bold;")
        stats_layout.addWidget(self.success_label, 0, 0)

        self.failed_label = QLabel("Thất bại: 0")
        self.failed_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        stats_layout.addWidget(self.failed_label, 0, 1)

        results_layout.addWidget(stats_group)

        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(3)
        self.results_table.setHorizontalHeaderLabels(["Email", "Mật khẩu", "Trạng thái"])
        self.results_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        results_layout.addWidget(self.results_table)

        # Export buttons
        export_layout = QHBoxLayout()

        export_success_btn = QPushButton("💾 Xuất tài khoản (email|pass|cookie)")
        export_success_btn.clicked.connect(self.export_successful_accounts)
        export_layout.addWidget(export_success_btn)

        export_all_btn = QPushButton("📄 Xuất tất cả")
        export_all_btn.clicked.connect(self.export_all_accounts)
        export_layout.addWidget(export_all_btn)

        export_layout.addStretch()
        results_layout.addLayout(export_layout)

        # Tab 3: Hướng dẫn
        guide_tab = QWidget()
        tab_widget.addTab(guide_tab, "📖 Hướng dẫn")

        guide_layout = QVBoxLayout(guide_tab)

        # Token guide
        token_guide = QGroupBox("🔑 Hướng dẫn lấy Token Tempmail")
        token_layout = QVBoxLayout(token_guide)

        token_text = QTextEdit()
        token_text.setReadOnly(True)
        token_text.setMaximumHeight(200)
        token_text.setPlainText("""
🔑 HƯỚNG DẪN LẤY TOKEN TEMPMAIL.ID.VN:

1. Truy cập: https://tempmail.id.vn
2. Đăng ký tài khoản mới (hoặc đăng nhập)
3. Vào Dashboard > API Settings
4. Copy token từ ô "API Token"
5. Paste vào ô "Token Tempmail" trong tool

💡 LƯU Ý:
- Mỗi token có giới hạn 10 emails/ngày
- Khi hết quota, cần tạo account mới
- Token miễn phí, không cần thanh toán
        """)
        token_text.setStyleSheet("background-color: #f8f9fa; font-family: 'Consolas', monospace;")
        token_layout.addWidget(token_text)

        guide_layout.addWidget(token_guide)

        # Usage guide
        usage_guide = QGroupBox("🚀 Hướng dẫn sử dụng Tool")
        usage_layout = QVBoxLayout(usage_guide)

        usage_text = QTextEdit()
        usage_text.setReadOnly(True)
        usage_text.setPlainText("""
🚀 HƯỚNG DẪN SỬ DỤNG:

1. Nhập mật khẩu cho tài khoản Kling AI
2. Nhập token tempmail (xem hướng dẫn trên)
3. Click "🧪 Test Token" để kiểm tra token
4. Chọn số lượng tài khoản muốn tạo
5. Click "🚀 Bắt đầu đăng ký"
6. Đợi tool hoàn thành
7. Xem kết quả trong tab "📊 Kết quả"
8. Export accounts ra file nếu cần

⚠️ LƯU Ý:
- Mỗi token chỉ tạo được 10 accounts
- Cần token mới khi hết quota
- Tool tự động retry khi có lỗi
- Kết quả được lưu vào account_output.txt
        """)
        usage_text.setStyleSheet("background-color: #f8f9fa; font-family: 'Consolas', monospace;")
        usage_layout.addWidget(usage_text)

        guide_layout.addWidget(usage_guide)

    def toggle_password_visibility(self):
        """Toggle password visibility"""
        if self.show_password_cb.isChecked():
            self.password_input.setEchoMode(QLineEdit.Normal)
        else:
            self.password_input.setEchoMode(QLineEdit.Password)

    def test_token(self):
        """Test token tempmail"""
        token = self.token_input.text().strip()
        if not token:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập token!")
            return

        # Disable button khi đang test
        self.test_token_btn.setEnabled(False)
        self.test_token_btn.setText("🔄 Testing...")

        # Test API call
        try:
            url = "https://tempmail.id.vn/api/email/create"
            headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {token}'
            }

            payload = {
                "user": "testuser123",
                "domain": "tempmail.ckvn.edu.vn"
            }

            response = requests.post(url, headers=headers, json=payload, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    QMessageBox.information(self, "Token hợp lệ",
                        f"✅ Token hoạt động tốt!\n\n"
                        f"Email test: {result.get('data', {}).get('email', 'N/A')}")
                else:
                    QMessageBox.warning(self, "Token lỗi",
                        f"❌ Token không hoạt động:\n{result.get('message', 'Unknown error')}")

            elif response.status_code == 400:
                try:
                    error_data = response.json()
                    error_message = error_data.get('message', '')

                    if 'giới hạn email' in error_message:
                        QMessageBox.warning(self, "Token hết quota",
                            f"⚠️ Token đã hết quota:\n{error_message}\n\n"
                            f"💡 Hướng dẫn:\n"
                            f"1. Tạo account mới tại tempmail.id.vn\n"
                            f"2. Lấy token mới từ dashboard\n"
                            f"3. Thay thế token cũ")
                    else:
                        QMessageBox.warning(self, "Token lỗi",
                            f"❌ Lỗi 400: {error_message}")
                except:
                    QMessageBox.warning(self, "Token lỗi",
                        f"❌ Lỗi 400: {response.text}")

            elif response.status_code == 401:
                QMessageBox.warning(self, "Token không hợp lệ",
                    f"❌ Token không hợp lệ hoặc đã hết hạn!\n\n"
                    f"💡 Hướng dẫn:\n"
                    f"1. Đăng nhập tempmail.id.vn\n"
                    f"2. Vào Dashboard > API\n"
                    f"3. Copy token mới")

            else:
                QMessageBox.warning(self, "Lỗi API",
                    f"❌ Lỗi {response.status_code}:\n{response.text}")

        except requests.exceptions.Timeout:
            QMessageBox.warning(self, "Timeout", "❌ Kết nối timeout! Kiểm tra internet.")

        except requests.exceptions.RequestException as e:
            QMessageBox.warning(self, "Lỗi kết nối", f"❌ Lỗi kết nối:\n{str(e)}")

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"❌ Lỗi không xác định:\n{str(e)}")

        finally:
            # Restore button
            self.test_token_btn.setEnabled(True)
            self.test_token_btn.setText("🧪 Test Token")

    def start_registration(self):
        """Bắt đầu đăng ký"""
        password = self.password_input.text().strip()
        if not password:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập mật khẩu!")
            return

        token = self.token_input.text().strip()
        if not token:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập token tempmail!")
            return

        count = self.count_input.value()

        # Reset
        self.successful_accounts.clear()
        self.failed_accounts.clear()
        self.results_table.setRowCount(0)
        self.progress_bar.setValue(0)
        self.update_statistics()

        # Start worker với token
        self.worker = EmailWorker(password, count, token)
        self.worker.log_signal.connect(self.add_log)
        self.worker.progress_signal.connect(self.progress_bar.setValue)
        self.worker.email_created_signal.connect(self.on_email_created)
        self.worker.registration_complete_signal.connect(self.on_registration_complete)
        self.worker.finished.connect(self.on_worker_finished)

        self.worker.start()

        # Update UI
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.add_log("🚀 Bắt đầu quá trình đăng ký...")

    def stop_registration(self):
        """Dừng đăng ký"""
        if self.worker:
            self.worker.stop()
            self.add_log("⏹️ Đang dừng quá trình...")

    def on_worker_finished(self):
        """Khi worker hoàn thành"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.add_log("✅ Hoàn thành quá trình đăng ký!")

    def on_email_created(self, email, mail_id):
        """Khi tạo email thành công"""
        # mail_id là int, có thể log hoặc lưu trữ nếu cần
        self.add_log(f"📧 Email được tạo: {email} (ID: {mail_id})")

    def on_registration_complete(self, success, account_info):
        """Khi hoàn thành đăng ký một tài khoản"""
        if success:
            try:
                # Parse JSON account info
                account_data = json.loads(account_info)
                email = account_data['email']
                password = account_data['password']
                cookie_string = account_data.get('cookie_string', '')

                # Lưu account với cookie string
                self.successful_accounts.append(account_data)
                self.add_account_to_table(email, password, "✅ Thành công")

                # Log cookie info
                if cookie_string:
                    cookie_count = len(cookie_string.split('; ')) if cookie_string else 0
                    self.add_log(f"🍪 Tài khoản {email} có {cookie_count} cookies")
                else:
                    self.add_log(f"⚠️ Tài khoản {email} không có cookies")

            except (json.JSONDecodeError, KeyError) as e:
                # Fallback cho format cũ
                self.add_log(f"⚠️ Lỗi parse account info: {e}")
                if ':' in account_info:
                    email, password = account_info.split(':', 1)
                    account_data = {'email': email, 'password': password, 'cookie_string': ''}
                    self.successful_accounts.append(account_data)
                    self.add_account_to_table(email, password, "✅ Thành công")
        else:
            self.failed_accounts.append(account_info)
            email = account_info.split(':')[0] if ':' in account_info else account_info
            self.add_account_to_table(email, "N/A", "❌ Thất bại")

        self.update_statistics()

    def add_account_to_table(self, email, password, status):
        """Thêm tài khoản vào bảng"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(email))
        self.results_table.setItem(row, 1, QTableWidgetItem(password))
        self.results_table.setItem(row, 2, QTableWidgetItem(status))

        # Color coding
        if "Thành công" in status:
            for col in range(3):
                item = self.results_table.item(row, col)
                item.setBackground(Qt.green)
        else:
            for col in range(3):
                item = self.results_table.item(row, col)
                item.setBackground(Qt.red)

    def update_statistics(self):
        """Cập nhật thống kê"""
        success_count = len(self.successful_accounts)
        failed_count = len(self.failed_accounts)

        self.success_label.setText(f"Thành công: {success_count}")
        self.failed_label.setText(f"Thất bại: {failed_count}")

    def add_log(self, message):
        """Thêm log"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.ensureCursorVisible()

    def clear_log(self):
        """Xóa log"""
        self.log_text.clear()

    def export_successful_accounts(self):
        """Xuất tài khoản thành công"""
        if not self.successful_accounts:
            QMessageBox.information(self, "Thông báo", "Không có tài khoản thành công để xuất!")
            return

        try:
            with open("account_output.txt", "w", encoding="utf-8") as f:
                f.write("# Tài khoản Kling AI đăng ký thành công\n")
                f.write("# Format: email|password|cookie\n\n")

                for account in self.successful_accounts:
                    if isinstance(account, dict):
                        email = account['email']
                        password = account['password']
                        cookie_string = account.get('cookie_string', '')

                        # Nếu không có cookies, để NO_COOKIES
                        if not cookie_string:
                            cookie_string = "NO_COOKIES"

                        f.write(f"{email}|{password}|{cookie_string}\n")
                    else:
                        # Fallback cho format cũ
                        email, password = account.split(':', 1)
                        f.write(f"{email}|{password}|NO_COOKIES\n")

            QMessageBox.information(self, "Thành công",
                                  f"Đã xuất {len(self.successful_accounts)} tài khoản vào file 'account_output.txt'\n"
                                  f"Format: email|password|cookie")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể xuất file: {str(e)}")

    def export_all_accounts(self):
        """Xuất tất cả tài khoản"""
        if not self.successful_accounts and not self.failed_accounts:
            QMessageBox.information(self, "Thông báo", "Không có tài khoản để xuất!")
            return

        try:
            with open("all_accounts.txt", "w", encoding="utf-8") as f:
                f.write("# Tất cả tài khoản Kling AI\n")
                f.write("# Format: email:password:status\n\n")

                f.write("# Tài khoản thành công:\n")
                for account in self.successful_accounts:
                    f.write(f"{account}:SUCCESS\n")

                f.write("\n# Tài khoản thất bại:\n")
                for account in self.failed_accounts:
                    f.write(f"{account}:FAILED\n")

            total = len(self.successful_accounts) + len(self.failed_accounts)
            QMessageBox.information(self, "Thành công",
                                  f"Đã xuất {total} tài khoản vào file 'all_accounts.txt'")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể xuất file: {str(e)}")

def main():
    app = QApplication(sys.argv)

    # Set application style
    app.setStyle('Fusion')

    # Set application icon (if you have one)
    # app.setWindowIcon(QIcon('icon.png'))

    window = KlingRegisterGUI()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
