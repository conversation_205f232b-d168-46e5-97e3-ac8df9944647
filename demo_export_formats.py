#!/usr/bin/env python3
"""
Demo script để test các format export khác nhau
"""

import json

def demo_export_formats():
    """Demo các format export"""
    print("🧪 Demo Export Formats...")
    
    # Sample account data
    sample_accounts = [
        {
            'user_id': '********',
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'cookie_string': 'weblogger_did=web_7487C0CC5D; __risk_web_device_id=***************; KLING_LAST_ACCESS_REGION=global; _did=web_A7628C21E33; did=web_836786635015207; userId=********; passToken=ChNrc2kxOG4uYWkucG9ydGFsLnN0EqABA; ak_bmsc=34F60501EE09938F827FD954DDEC48C8~000000000000000000000000000000~YAAQ'
        },
        {
            'user_id': '********',
            'email': '<EMAIL>',
            'password': 'TestPass456!',
            'cookie_string': 'weblogger_did=web_8598BDD6E3; __risk_web_device_id=***************; KLING_LAST_ACCESS_REGION=global; _did=web_B8739D32F44; did=web_947314366672571; userId=********; passToken=ChNrc2kxOG4uYWkucG9ydGFsLnN0EqABB; ak_bmsc=45G71612FF19A48G938E5FFGF7E1CF39~111111111111111111111111111111~YAAQ'
        }
    ]
    
    formats = [
        "ID|Email|Pass|Cookie",
        "Email|Pass|Cookie", 
        "Email|Pass",
        "Cookie only"
    ]
    
    for format_name in formats:
        print(f"\n📋 Format: {format_name}")
        print("=" * 50)
        
        for i, account in enumerate(sample_accounts, 1):
            user_id = account['user_id']
            email = account['email']
            password = account['password']
            cookie_string = account['cookie_string']
            
            if format_name == "ID|Email|Pass|Cookie":
                output = f"{user_id}|{email}|{password}|{cookie_string}"
            elif format_name == "Email|Pass|Cookie":
                output = f"{email}|{password}|{cookie_string}"
            elif format_name == "Email|Pass":
                output = f"{email}|{password}"
            elif format_name == "Cookie only":
                output = cookie_string
            
            print(f"Account {i}: {output[:100]}{'...' if len(output) > 100 else ''}")
    
    # Demo file creation
    print(f"\n💾 Demo file creation...")
    
    format_mapping = {
        "ID|Email|Pass|Cookie": "account_output_full.txt",
        "Email|Pass|Cookie": "account_output_with_cookie.txt",
        "Email|Pass": "account_output_simple.txt",
        "Cookie only": "cookies_only.txt"
    }
    
    for format_name, filename in format_mapping.items():
        print(f"\n📁 Creating {filename} with format: {format_name}")
        
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write("# Tài khoản Kling AI đăng ký thành công\n")
                f.write(f"# Format: {format_name}\n")
                f.write("# Cookies bao gồm cả HttpOnly và tất cả path\n\n")

                for account in sample_accounts:
                    user_id = account['user_id']
                    email = account['email']
                    password = account['password']
                    cookie_string = account['cookie_string']

                    # Xuất theo format
                    if format_name == "ID|Email|Pass|Cookie":
                        f.write(f"{user_id}|{email}|{password}|{cookie_string}\n")
                    elif format_name == "Email|Pass|Cookie":
                        f.write(f"{email}|{password}|{cookie_string}\n")
                    elif format_name == "Email|Pass":
                        f.write(f"{email}|{password}\n")
                    elif format_name == "Cookie only":
                        f.write(f"{cookie_string}\n")
            
            print(f"✅ Created {filename}")
            
        except Exception as e:
            print(f"❌ Error creating {filename}: {str(e)}")
    
    print(f"\n🎉 Demo completed! Check the generated files:")
    for filename in format_mapping.values():
        print(f"  - {filename}")

if __name__ == "__main__":
    demo_export_formats()
