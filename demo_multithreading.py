#!/usr/bin/env python3
"""
Demo script để test tính năng đa luồng
"""

import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import random

def simulate_account_creation(worker_id, delay_range=(5, 15)):
    """<PERSON><PERSON><PERSON> lập quá trình tạo tài khoản"""
    delay = random.uniform(*delay_range)
    
    print(f"🔄 Worker {worker_id}: Bắt đầu tạo tài khoản...")
    
    # <PERSON><PERSON><PERSON> lập các bước
    steps = [
        "Tạo random email",
        "Gửi yêu cầu mã xác thực", 
        "Đợi email được gửi",
        "<PERSON><PERSON>y mã xác thực từ tempmail",
        "Đăng ký tài khoản",
        "Extract cookies"
    ]
    
    step_delay = delay / len(steps)
    
    for i, step in enumerate(steps, 1):
        time.sleep(step_delay)
        print(f"   Worker {worker_id}: <PERSON><PERSON><PERSON><PERSON> {i}/{len(steps)} - {step}")
    
    # <PERSON><PERSON><PERSON> lập kết quả
    success = random.choice([True, True, True, False])  # 75% success rate
    
    if success:
        email = f"test{worker_id}@simpace.edu.vn"
        print(f"✅ Worker {worker_id}: Thành công - {email}")
        return True, f"SUCCESS:{email}"
    else:
        print(f"❌ Worker {worker_id}: Thất bại")
        return False, f"FAILED:Worker{worker_id}"

def demo_single_thread(count=5):
    """Demo chạy tuần tự (1 luồng)"""
    print(f"\n🐌 DEMO SINGLE THREAD - {count} accounts")
    print("=" * 50)
    
    start_time = time.time()
    results = []
    
    for i in range(count):
        success, result = simulate_account_creation(i + 1)
        results.append((success, result))
    
    end_time = time.time()
    total_time = end_time - start_time
    
    successful = sum(1 for success, _ in results if success)
    failed = count - successful
    
    print(f"\n📊 KẾT QUẢ SINGLE THREAD:")
    print(f"   Thời gian: {total_time:.1f} giây")
    print(f"   Thành công: {successful}/{count}")
    print(f"   Thất bại: {failed}/{count}")
    print(f"   Tốc độ: {count/total_time:.2f} accounts/giây")
    
    return total_time, successful, failed

def demo_multi_thread(count=5, max_threads=3):
    """Demo chạy đa luồng"""
    print(f"\n🚀 DEMO MULTI THREAD - {count} accounts với {max_threads} luồng")
    print("=" * 50)
    
    start_time = time.time()
    results = []
    completed_count = 0
    lock = threading.Lock()
    
    def worker_task(worker_id):
        nonlocal completed_count
        success, result = simulate_account_creation(worker_id)
        
        with lock:
            completed_count += 1
            progress = int((completed_count / count) * 100)
            print(f"📈 Progress: {progress}% ({completed_count}/{count})")
        
        return success, result
    
    # Sử dụng ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=max_threads) as executor:
        # Submit tất cả tasks
        futures = []
        for i in range(count):
            future = executor.submit(worker_task, i + 1)
            futures.append(future)
            time.sleep(0.5)  # Delay nhỏ giữa các task
        
        # Đợi tất cả tasks hoàn thành
        for future in as_completed(futures):
            try:
                success, result = future.result()
                results.append((success, result))
            except Exception as e:
                print(f"❌ Task error: {str(e)}")
                results.append((False, f"ERROR:{str(e)}"))
    
    end_time = time.time()
    total_time = end_time - start_time
    
    successful = sum(1 for success, _ in results if success)
    failed = count - successful
    
    print(f"\n📊 KẾT QUẢ MULTI THREAD:")
    print(f"   Thời gian: {total_time:.1f} giây")
    print(f"   Thành công: {successful}/{count}")
    print(f"   Thất bại: {failed}/{count}")
    print(f"   Tốc độ: {count/total_time:.2f} accounts/giây")
    print(f"   Luồng: {max_threads}")
    
    return total_time, successful, failed

def compare_performance():
    """So sánh hiệu suất giữa single thread và multi thread"""
    print("🧪 DEMO MULTITHREADING PERFORMANCE")
    print("=" * 60)
    
    count = 5
    
    # Test single thread
    single_time, single_success, single_failed = demo_single_thread(count)
    
    # Test multi thread với các số luồng khác nhau
    thread_counts = [2, 3, 5]
    multi_results = []
    
    for threads in thread_counts:
        multi_time, multi_success, multi_failed = demo_multi_thread(count, threads)
        multi_results.append((threads, multi_time, multi_success, multi_failed))
    
    # So sánh kết quả
    print(f"\n🏆 SO SÁNH HIỆU SUẤT:")
    print("=" * 60)
    print(f"Single Thread:  {single_time:.1f}s | {count/single_time:.2f} acc/s | Success: {single_success}")
    
    for threads, multi_time, multi_success, multi_failed in multi_results:
        speedup = single_time / multi_time
        print(f"{threads} Threads:     {multi_time:.1f}s | {count/multi_time:.2f} acc/s | Success: {multi_success} | Speedup: {speedup:.1f}x")
    
    # Tìm cấu hình tốt nhất
    best_threads, best_time, best_success, best_failed = max(multi_results, key=lambda x: x[2]/x[1])  # success/time ratio
    best_speedup = single_time / best_time
    
    print(f"\n🎯 KHUYẾN NGHỊ:")
    print(f"   Cấu hình tốt nhất: {best_threads} luồng")
    print(f"   Tăng tốc: {best_speedup:.1f}x so với single thread")
    print(f"   Thời gian: {best_time:.1f}s vs {single_time:.1f}s")

if __name__ == "__main__":
    compare_performance()
