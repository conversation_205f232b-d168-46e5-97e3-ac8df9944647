#!/usr/bin/env python3
"""
Test script để kiểm tra session cookies
"""

import requests
from urllib.parse import quote
import random
import string
import json

def test_session_cookies():
    """Test session cookies workflow"""
    print("🧪 Testing Session Cookies...")
    
    # Tạo session
    session = requests.Session()
    
    # Setup headers
    session.headers.update({
        'accept': '*/*',
        'accept-language': 'vi,en-US;q=0.9,en;q=0.8',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    })
    
    # Tạo initial cookies
    session.cookies.set('weblogger_did', f'web_{random.randint(100000000000, 999999999999):X}', domain='.klingai.com')
    session.cookies.set('__risk_web_device_id', f'{random.randint(100000000000000, 999999999999999)}', domain='.klingai.com')
    session.cookies.set('KLING_LAST_ACCESS_REGION', 'global', domain='.klingai.com')
    session.cookies.set('_did', f'web_{random.randint(10000000000000, 99999999999999):X}', domain='.klingai.com')
    session.cookies.set('did', f'web_{random.randint(100000000000000, 999999999999999)}', domain='.klingai.com')
    
    print(f"🍪 Initial cookies: {len(session.cookies)} cookies")
    for cookie in session.cookies:
        print(f"  - {cookie.name}: {cookie.value[:50]}...")
    
    # Test email
    email = "<EMAIL>"
    
    # Gửi code request
    print(f"\n📨 Sending code request for {email}...")
    url = "https://id.klingai.com/pass/ksi18n/web/email/code?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR85WymFzHoNZcUWNcTiC8KvwzS6UWycXZhGA6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UjA8hnlVOU.$HE_f2ed751012bfb45f292eb827258364dd73b8b9b9b9b870211503a967cd0a55321ae12eb822ef8763c2ef8751b9&caver=2"
    
    encoded_email = quote(email, safe='')
    payload = f'sid=ksi18n.ai.portal&email={encoded_email}&type=439&language=en&isWebSig4=true'
    
    headers = {
        'content-type': 'application/x-www-form-urlencoded',
        'fe-id': '4d0021ac1750482685583251',
        'fe-kpf': 'PC',
        'fe-kpn': 'KLING',
        'origin': 'https://app.klingai.com',
        'referer': 'https://app.klingai.com/',
    }
    
    try:
        response = session.post(url, headers=headers, data=payload, timeout=10)
        print(f"✅ Code request status: {response.status_code}")
        print(f"🍪 Cookies after code request: {len(session.cookies)} cookies")
        
        # Show new cookies
        for cookie in session.cookies:
            print(f"  - {cookie.name}: {cookie.value[:50]}...")
        
        # Extract cookies to string
        cookie_list = []
        for cookie in session.cookies:
            cookie_list.append(f"{cookie.name}={cookie.value}")
        
        cookie_string = '; '.join(cookie_list)
        print(f"\n🍪 Cookie string length: {len(cookie_string)} chars")
        print(f"🍪 Cookie string preview: {cookie_string[:200]}...")
        
        # Check important cookies
        important_cookies = ['weblogger_did', '__risk_web_device_id', 'KLING_LAST_ACCESS_REGION', '_did', 'did']
        print(f"\n🔍 Important cookies check:")
        for cookie_name in important_cookies:
            found = any(cookie.name == cookie_name for cookie in session.cookies)
            print(f"  - {cookie_name}: {'✅' if found else '❌'}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_session_cookies()
