import sys
import os
import threading
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLineEdit, QTextEdit, QLabel, 
                             QFileDialog, QProgressBar, QGroupBox, QGridLayout,
                             QMessageBox, QFrame)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
from kling_auto import KlingAI

class WorkerThread(QThread):
    """Worker thread để chạy Kling AI workflow không block UI"""
    progress_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)
    
    def __init__(self, cookie, file_path, prompt):
        super().__init__()
        self.cookie = cookie
        self.file_path = file_path
        self.prompt = prompt
        
    def run(self):
        try:
            self.progress_signal.emit("🚀 Khởi tạo Kling AI...")
            kling = KlingAI(self.cookie)
            
            self.progress_signal.emit("🎬 Bắt đầu tạo video...")
            result = kling.create_video_from_image(self.file_path, self.prompt)
            
            if result:
                self.finished_signal.emit(True, f"✅ Thành công: {result}")
            else:
                self.finished_signal.emit(False, "❌ Tạo video thất bại")
                
        except Exception as e:
            self.finished_signal.emit(False, f"❌ Lỗi: {str(e)}")

class KlingGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.worker_thread = None
        self.init_ui()
        
    def init_ui(self):
        """Khởi tạo giao diện"""
        self.setWindowTitle("Kling AI Video Generator")
        self.setGeometry(100, 100, 800, 600)
        self.setStyleSheet(self.get_stylesheet())
        
        # Widget chính
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout chính
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Header
        self.create_header(main_layout)
        
        # Input section
        self.create_input_section(main_layout)
        
        # Control buttons
        self.create_control_section(main_layout)
        
        # Progress section
        self.create_progress_section(main_layout)
        
        # Output section
        self.create_output_section(main_layout)
        
    def create_header(self, parent_layout):
        """Tạo header"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Box)
        header_frame.setStyleSheet("background-color: #2c3e50; border-radius: 10px;")
        
        header_layout = QVBoxLayout(header_frame)
        
        title_label = QLabel("🎬 Kling AI Video Generator")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: white; font-size: 24px; font-weight: bold; padding: 15px;")
        
        subtitle_label = QLabel("Tạo video từ hình ảnh với AI")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("color: #ecf0f1; font-size: 14px; padding-bottom: 10px;")
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        parent_layout.addWidget(header_frame)
        
    def create_input_section(self, parent_layout):
        """Tạo section nhập liệu"""
        input_group = QGroupBox("📝 Thông tin đầu vào")
        input_group.setStyleSheet("QGroupBox { font-weight: bold; font-size: 14px; }")
        input_layout = QGridLayout(input_group)
        
        # Cookie input
        cookie_label = QLabel("🍪 Cookie:")
        cookie_layout = QHBoxLayout()
        self.cookie_input = QLineEdit()
        self.cookie_input.setPlaceholderText("Nhập cookie authentication từ Kling AI...")
        self.cookie_input.setEchoMode(QLineEdit.Password)

        self.validate_cookie_btn = QPushButton("🔍 Kiểm tra")
        self.validate_cookie_btn.clicked.connect(self.validate_cookie)
        self.validate_cookie_btn.setStyleSheet("QPushButton { background-color: #9b59b6; color: white; padding: 8px; border-radius: 5px; }")

        cookie_layout.addWidget(self.cookie_input)
        cookie_layout.addWidget(self.validate_cookie_btn)
        
        # File path input
        file_label = QLabel("🖼️ Hình ảnh:")
        file_layout = QHBoxLayout()
        self.file_input = QLineEdit()
        self.file_input.setPlaceholderText("Chọn file hình ảnh...")
        self.file_input.setReadOnly(True)
        
        self.browse_btn = QPushButton("📁 Chọn file")
        self.browse_btn.clicked.connect(self.browse_file)
        self.browse_btn.setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 8px; border-radius: 5px; }")
        
        file_layout.addWidget(self.file_input)
        file_layout.addWidget(self.browse_btn)
        
        # Prompt input
        prompt_label = QLabel("💭 Prompt:")
        self.prompt_input = QTextEdit()
        self.prompt_input.setPlaceholderText("Nhập mô tả cho video muốn tạo...")
        self.prompt_input.setMaximumHeight(100)
        
        # Add to layout
        input_layout.addWidget(cookie_label, 0, 0)
        input_layout.addLayout(cookie_layout, 0, 1)
        input_layout.addWidget(file_label, 1, 0)
        input_layout.addLayout(file_layout, 1, 1)
        input_layout.addWidget(prompt_label, 2, 0)
        input_layout.addWidget(self.prompt_input, 2, 1)
        
        parent_layout.addWidget(input_group)
        
    def create_control_section(self, parent_layout):
        """Tạo section điều khiển"""
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("🚀 Tạo Video")
        self.start_btn.clicked.connect(self.start_process)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 30px;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        
        self.stop_btn = QPushButton("⏹️ Dừng")
        self.stop_btn.clicked.connect(self.stop_process)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 30px;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        
        self.clear_btn = QPushButton("🗑️ Xóa")
        self.clear_btn.clicked.connect(self.clear_all)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px 30px;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.clear_btn)
        control_layout.addStretch()
        
        parent_layout.addLayout(control_layout)
        
    def create_progress_section(self, parent_layout):
        """Tạo section tiến trình"""
        progress_group = QGroupBox("📊 Tiến trình")
        progress_group.setStyleSheet("QGroupBox { font-weight: bold; font-size: 14px; }")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 6px;
            }
        """)
        
        self.status_label = QLabel("⏳ Sẵn sàng...")
        self.status_label.setStyleSheet("font-size: 14px; color: #2c3e50; padding: 5px;")
        
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        
        parent_layout.addWidget(progress_group)
        
    def create_output_section(self, parent_layout):
        """Tạo section kết quả"""
        output_group = QGroupBox("📋 Kết quả")
        output_group.setStyleSheet("QGroupBox { font-weight: bold; font-size: 14px; }")
        output_layout = QVBoxLayout(output_group)
        
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        self.output_text.setStyleSheet("""
            QTextEdit {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-family: 'Consolas', monospace;
                font-size: 12px;
                padding: 10px;
            }
        """)
        
        output_layout.addWidget(self.output_text)
        parent_layout.addWidget(output_group)

    def get_stylesheet(self):
        """Trả về stylesheet cho ứng dụng"""
        return """
            QMainWindow {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """

    def browse_file(self):
        """Chọn file hình ảnh"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Chọn hình ảnh",
            "",
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif *.tiff)"
        )
        if file_path:
            self.file_input.setText(file_path)
            self.log_message(f"📁 Đã chọn file: {os.path.basename(file_path)}")

    def validate_cookie(self):
        """Kiểm tra tính hợp lệ của cookie"""
        cookie = self.cookie_input.text().strip()
        if not cookie:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập cookie trước!")
            return

        try:
            from kling_auto import KlingAI
            kling = KlingAI(cookie)

            self.log_message("🔍 Đang kiểm tra cookie...")

            # Validate cookie
            is_valid = kling.validate_cookie()

            # Parse cookie info
            cookie_info = kling.parse_cookie_info()

            # Display results
            if is_valid:
                QMessageBox.information(self, "Cookie hợp lệ",
                    f"✅ Cookie đang hoạt động!\n\n"
                    f"📊 Thống kê:\n"
                    f"• Tổng cookies: {cookie_info['total_cookies']}\n"
                    f"• Critical cookies: {len(cookie_info['critical_cookies'])}\n"
                    f"• Session cookies: {len(cookie_info['session_cookies'])}\n"
                    f"• Expired cookies: {len(cookie_info['expired_cookies'])}")

                self.log_message("✅ Cookie hợp lệ và đang hoạt động")
            else:
                QMessageBox.warning(self, "Cookie không hợp lệ",
                    f"❌ Cookie đã hết hạn hoặc không hợp lệ!\n\n"
                    f"📊 Thống kê:\n"
                    f"• Tổng cookies: {cookie_info['total_cookies']}\n"
                    f"• Expired cookies: {len(cookie_info['expired_cookies'])}\n"
                    f"• Critical cookies: {len(cookie_info['critical_cookies'])}\n\n"
                    f"💡 Hướng dẫn:\n"
                    f"1. Đăng nhập lại Kling AI\n"
                    f"2. Copy cookie mới từ browser\n"
                    f"3. Paste vào ô cookie")

                self.log_message("❌ Cookie không hợp lệ - cần cập nhật")

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể kiểm tra cookie:\n{str(e)}")
            self.log_message(f"❌ Lỗi kiểm tra cookie: {str(e)}")

    def validate_inputs(self):
        """Kiểm tra tính hợp lệ của input"""
        if not self.cookie_input.text().strip():
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập cookie!")
            return False

        if not self.file_input.text().strip():
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn file hình ảnh!")
            return False

        if not os.path.exists(self.file_input.text()):
            QMessageBox.warning(self, "Lỗi", "File hình ảnh không tồn tại!")
            return False

        if not self.prompt_input.toPlainText().strip():
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập prompt!")
            return False

        return True

    def start_process(self):
        """Bắt đầu quá trình tạo video"""
        if not self.validate_inputs():
            return

        # Disable start button, enable stop button
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

        # Clear output
        self.output_text.clear()

        # Start progress
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.status_label.setText("🚀 Đang khởi tạo...")

        # Create and start worker thread
        self.worker_thread = WorkerThread(
            self.cookie_input.text().strip(),
            self.file_input.text().strip(),
            self.prompt_input.toPlainText().strip()
        )

        # Connect signals
        self.worker_thread.progress_signal.connect(self.update_progress)
        self.worker_thread.finished_signal.connect(self.process_finished)

        # Start thread
        self.worker_thread.start()

        self.log_message("🚀 Bắt đầu quá trình tạo video...")

    def stop_process(self):
        """Dừng quá trình"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.terminate()
            self.worker_thread.wait()

        self.process_finished(False, "⏹️ Đã dừng bởi người dùng")

    def clear_all(self):
        """Xóa tất cả dữ liệu"""
        self.cookie_input.clear()
        self.file_input.clear()
        self.prompt_input.clear()
        self.output_text.clear()
        self.progress_bar.setValue(0)
        self.status_label.setText("⏳ Sẵn sàng...")
        self.log_message("🗑️ Đã xóa tất cả dữ liệu")

    def update_progress(self, message):
        """Cập nhật tiến trình"""
        self.status_label.setText(message)
        self.log_message(message)

    def process_finished(self, success, message):
        """Xử lý khi quá trình hoàn thành"""
        # Reset UI
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setRange(0, 100)

        if success:
            self.progress_bar.setValue(100)
            self.status_label.setText("✅ Hoàn thành!")
            QMessageBox.information(self, "Thành công", "Video đã được tạo thành công!")
        else:
            self.progress_bar.setValue(0)
            self.status_label.setText("❌ Thất bại!")

        self.log_message(message)

    def log_message(self, message):
        """Ghi log message"""
        self.output_text.append(f"[{self.get_current_time()}] {message}")
        self.output_text.ensureCursorVisible()

    def get_current_time(self):
        """Lấy thời gian hiện tại"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")


def main():
    """Hàm main"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Kling AI Video Generator")
    app.setApplicationVersion("1.0")

    # Create and show main window
    window = KlingGUI()
    window.show()

    # Run application
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
